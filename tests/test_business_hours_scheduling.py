"""
Tests for business hours scheduling functionality.
"""
import pytest
import os
import sys
from unittest.mock import patch, Mock
from datetime import datetime
import pytz

# Add the parent directory to the path to import sqlscript
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import sqlscript


class TestAttendanceConfigBusinessHours:
    """Test AttendanceConfig class business hours configuration."""
    
    def test_default_business_hours_config(self, mock_env_vars):
        """Test default business hours configuration."""
        with patch.dict(os.environ, mock_env_vars, clear=True):
            config = sqlscript.AttendanceConfig()
            
            assert config.business_hours_enabled is True
            assert config.business_start_hour == 7
            assert config.business_start_minute == 30
            assert config.business_end_hour == 17
            assert config.business_end_minute == 0
            assert config.business_days == [1, 2, 3, 4, 5]  # Mon-Fri
            assert config.business_hours_interval == 5
            assert config.off_hours_interval == 60
    
    def test_custom_business_hours_config(self, mock_env_vars):
        """Test custom business hours configuration."""
        custom_env = mock_env_vars.copy()
        custom_env.update({
            'BUSINESS_HOURS_ENABLED': 'true',
            'BUSINESS_START_HOUR': '8',
            'BUSINESS_START_MINUTE': '0',
            'BUSINESS_END_HOUR': '16',
            'BUSINESS_END_MINUTE': '30',
            'BUSINESS_DAYS': '1,2,3,4,5,6',  # Mon-Sat
            'BUSINESS_HOURS_INTERVAL': '10',
            'OFF_HOURS_INTERVAL': '120'
        })
        
        with patch.dict(os.environ, custom_env, clear=True):
            config = sqlscript.AttendanceConfig()
            
            assert config.business_hours_enabled is True
            assert config.business_start_hour == 8
            assert config.business_start_minute == 0
            assert config.business_end_hour == 16
            assert config.business_end_minute == 30
            assert config.business_days == [1, 2, 3, 4, 5, 6]
            assert config.business_hours_interval == 10
            assert config.off_hours_interval == 120
    
    def test_disabled_business_hours(self, mock_env_vars):
        """Test disabled business hours configuration."""
        custom_env = mock_env_vars.copy()
        custom_env['BUSINESS_HOURS_ENABLED'] = 'false'
        
        with patch.dict(os.environ, custom_env, clear=True):
            config = sqlscript.AttendanceConfig()
            assert config.business_hours_enabled is False
    
    def test_invalid_business_hours_validation(self, mock_env_vars):
        """Test validation of invalid business hours configuration."""
        # Test invalid start hour
        invalid_env = mock_env_vars.copy()
        invalid_env['BUSINESS_START_HOUR'] = '25'  # Invalid hour
        
        with patch.dict(os.environ, invalid_env, clear=True):
            with pytest.raises(sqlscript.ConfigurationError, match="Invalid business start hour"):
                sqlscript.AttendanceConfig()
        
        # Test invalid business days
        invalid_env = mock_env_vars.copy()
        invalid_env['BUSINESS_DAYS'] = '0,8,9'  # Invalid days
        
        with patch.dict(os.environ, invalid_env, clear=True):
            with pytest.raises(sqlscript.ConfigurationError, match="Invalid business days"):
                sqlscript.AttendanceConfig()
        
        # Test invalid interval
        invalid_env = mock_env_vars.copy()
        invalid_env['BUSINESS_HOURS_INTERVAL'] = '0'  # Invalid interval
        
        with patch.dict(os.environ, invalid_env, clear=True):
            with pytest.raises(sqlscript.ConfigurationError, match="Business hours interval must be at least 1 minute"):
                sqlscript.AttendanceConfig()


class TestBusinessHoursScheduler:
    """Test BusinessHoursScheduler class."""
    
    @pytest.fixture
    def config_with_business_hours(self, mock_env_vars):
        """Fixture for config with business hours enabled."""
        env_vars = mock_env_vars.copy()
        env_vars.update({
            'BUSINESS_HOURS_ENABLED': 'true',
            'BUSINESS_START_HOUR': '8',
            'BUSINESS_START_MINUTE': '0',
            'BUSINESS_END_HOUR': '17',
            'BUSINESS_END_MINUTE': '0',
            'BUSINESS_DAYS': '1,2,3,4,5',  # Mon-Fri
            'BUSINESS_HOURS_INTERVAL': '5',
            'OFF_HOURS_INTERVAL': '60'
        })
        
        with patch.dict(os.environ, env_vars, clear=True):
            return sqlscript.AttendanceConfig()
    
    @pytest.fixture
    def config_without_business_hours(self, mock_env_vars):
        """Fixture for config with business hours disabled."""
        env_vars = mock_env_vars.copy()
        env_vars['BUSINESS_HOURS_ENABLED'] = 'false'
        
        with patch.dict(os.environ, env_vars, clear=True):
            return sqlscript.AttendanceConfig()
    
    def test_is_business_hours_during_work_time(self, config_with_business_hours):
        """Test business hours detection during work time."""
        scheduler = sqlscript.BusinessHoursScheduler(config_with_business_hours)
        tz = pytz.timezone('Pacific/Guadalcanal')
        
        # Monday 10:00 AM - should be business hours
        test_time = tz.localize(datetime(2024, 7, 29, 10, 0))  # Monday
        assert scheduler.is_business_hours(test_time) is True
        
        # Friday 4:00 PM - should be business hours
        test_time = tz.localize(datetime(2024, 8, 2, 16, 0))  # Friday
        assert scheduler.is_business_hours(test_time) is True
    
    def test_is_business_hours_outside_work_time(self, config_with_business_hours):
        """Test business hours detection outside work time."""
        scheduler = sqlscript.BusinessHoursScheduler(config_with_business_hours)
        tz = pytz.timezone('Pacific/Guadalcanal')
        
        # Monday 7:00 AM - before business hours
        test_time = tz.localize(datetime(2024, 7, 29, 7, 0))
        assert scheduler.is_business_hours(test_time) is False
        
        # Monday 6:00 PM - after business hours
        test_time = tz.localize(datetime(2024, 7, 29, 18, 0))
        assert scheduler.is_business_hours(test_time) is False
        
        # Saturday 10:00 AM - weekend
        test_time = tz.localize(datetime(2024, 8, 3, 10, 0))  # Saturday
        assert scheduler.is_business_hours(test_time) is False
        
        # Sunday 2:00 PM - weekend
        test_time = tz.localize(datetime(2024, 8, 4, 14, 0))  # Sunday
        assert scheduler.is_business_hours(test_time) is False
    
    def test_is_business_hours_boundary_times(self, config_with_business_hours):
        """Test business hours detection at boundary times."""
        scheduler = sqlscript.BusinessHoursScheduler(config_with_business_hours)
        tz = pytz.timezone('Pacific/Guadalcanal')
        
        # Exactly at start time - should be business hours
        test_time = tz.localize(datetime(2024, 7, 29, 8, 0))  # Monday 8:00 AM
        assert scheduler.is_business_hours(test_time) is True
        
        # Exactly at end time - should be business hours
        test_time = tz.localize(datetime(2024, 7, 29, 17, 0))  # Monday 5:00 PM
        assert scheduler.is_business_hours(test_time) is True
        
        # One minute before start - should not be business hours
        test_time = tz.localize(datetime(2024, 7, 29, 7, 59))
        assert scheduler.is_business_hours(test_time) is False
        
        # One minute after end - should not be business hours
        test_time = tz.localize(datetime(2024, 7, 29, 17, 1))
        assert scheduler.is_business_hours(test_time) is False
    
    def test_is_business_hours_disabled(self, config_without_business_hours):
        """Test business hours when disabled - should always return True."""
        scheduler = sqlscript.BusinessHoursScheduler(config_without_business_hours)
        tz = pytz.timezone('Pacific/Guadalcanal')
        
        # Any time should be considered business hours when disabled
        test_times = [
            datetime(2024, 7, 29, 2, 0),   # Monday 2:00 AM
            datetime(2024, 8, 3, 22, 0),  # Saturday 10:00 PM
            datetime(2024, 8, 4, 14, 0),  # Sunday 2:00 PM
        ]
        
        for test_time in test_times:
            localized_time = tz.localize(test_time)
            assert scheduler.is_business_hours(localized_time) is True
    
    def test_get_current_interval(self, config_with_business_hours):
        """Test getting current interval based on business hours."""
        scheduler = sqlscript.BusinessHoursScheduler(config_with_business_hours)
        
        # Mock is_business_hours to return True
        with patch.object(scheduler, 'is_business_hours', return_value=True):
            assert scheduler.get_current_interval() == 5
        
        # Mock is_business_hours to return False
        with patch.object(scheduler, 'is_business_hours', return_value=False):
            assert scheduler.get_current_interval() == 60
    
    def test_get_status_message(self, config_with_business_hours, config_without_business_hours):
        """Test status message generation."""
        # Test with business hours enabled
        scheduler = sqlscript.BusinessHoursScheduler(config_with_business_hours)
        
        with patch.object(scheduler, 'is_business_hours', return_value=True):
            message = scheduler.get_status_message()
            assert "Business hours - running every 5 minutes" in message
        
        with patch.object(scheduler, 'is_business_hours', return_value=False):
            message = scheduler.get_status_message()
            assert "Off hours - running every 60 minutes" in message
        
        # Test with business hours disabled
        scheduler_disabled = sqlscript.BusinessHoursScheduler(config_without_business_hours)
        message = scheduler_disabled.get_status_message()
        assert "24/7 mode - running every 5 minutes" in message


class TestSmartSchedulingFunctions:
    """Test smart scheduling functions."""

    @pytest.fixture
    def mock_scheduler(self):
        """Fixture for mock scheduler."""
        mock_scheduler = Mock()
        mock_scheduler.log_status_if_changed = Mock()
        mock_scheduler.is_business_hours = Mock()
        mock_scheduler.get_current_interval = Mock()
        return mock_scheduler

    @pytest.fixture
    def mock_config(self):
        """Fixture for mock config."""
        mock_config = Mock()
        mock_config.off_hours_interval = 60
        mock_config.business_hours_enabled = True
        return mock_config

    def test_smart_transfer_during_business_hours(self, mock_env_vars):
        """Test smart transfer function during business hours."""
        with patch.dict(os.environ, mock_env_vars, clear=True):
            with patch('sqlscript.scheduler') as mock_scheduler:
                with patch('sqlscript.config') as mock_config:
                    with patch('sqlscript.transfer_attendance_data') as mock_transfer:
                        # Setup mocks
                        mock_scheduler.log_status_if_changed = Mock()
                        mock_scheduler.is_business_hours.return_value = True
                        mock_config.off_hours_interval = 60

                        # Call function
                        sqlscript.smart_transfer_attendance_data()

                        # Verify calls
                        mock_scheduler.log_status_if_changed.assert_called_once()
                        mock_scheduler.is_business_hours.assert_called_once()
                        mock_transfer.assert_called_once()

    def test_smart_transfer_during_off_hours_with_interval(self, mock_env_vars):
        """Test smart transfer function during off-hours with interval > 0."""
        with patch.dict(os.environ, mock_env_vars, clear=True):
            with patch('sqlscript.scheduler') as mock_scheduler:
                with patch('sqlscript.config') as mock_config:
                    with patch('sqlscript.transfer_attendance_data') as mock_transfer:
                        # Setup mocks
                        mock_scheduler.log_status_if_changed = Mock()
                        mock_scheduler.is_business_hours.return_value = False
                        mock_config.off_hours_interval = 60

                        # Call function
                        sqlscript.smart_transfer_attendance_data()

                        # Verify calls
                        mock_scheduler.log_status_if_changed.assert_called_once()
                        mock_scheduler.is_business_hours.assert_called_once()
                        mock_transfer.assert_called_once()

    def test_smart_transfer_during_off_hours_disabled(self, mock_env_vars):
        """Test smart transfer function during off-hours with interval = 0."""
        with patch.dict(os.environ, mock_env_vars, clear=True):
            with patch('sqlscript.scheduler') as mock_scheduler:
                with patch('sqlscript.config') as mock_config:
                    with patch('sqlscript.transfer_attendance_data') as mock_transfer:
                        # Setup mocks
                        mock_scheduler.log_status_if_changed = Mock()
                        mock_scheduler.is_business_hours.return_value = False
                        mock_config.off_hours_interval = 0  # Disabled

                        # Call function
                        sqlscript.smart_transfer_attendance_data()

                        # Verify calls
                        mock_scheduler.log_status_if_changed.assert_called_once()
                        mock_scheduler.is_business_hours.assert_called_once()
                        mock_transfer.assert_not_called()  # Should not transfer when disabled

    def test_smart_scheduler_components(self, mock_env_vars):
        """Test individual components of the smart scheduler."""
        with patch.dict(os.environ, mock_env_vars, clear=True):
            # Test that the function exists and can be imported
            assert hasattr(sqlscript, 'run_smart_scheduler')
            assert callable(sqlscript.run_smart_scheduler)

            # Test that smart_transfer_attendance_data exists
            assert hasattr(sqlscript, 'smart_transfer_attendance_data')
            assert callable(sqlscript.smart_transfer_attendance_data)

    def test_scheduler_interval_logic(self, mock_env_vars):
        """Test the interval change logic separately."""
        with patch.dict(os.environ, mock_env_vars, clear=True):
            with patch('sqlscript.schedule') as mock_schedule:
                # Test interval change detection
                last_interval = None
                current_interval = 5

                # Simulate the logic from run_smart_scheduler
                if current_interval != last_interval:
                    mock_schedule.clear()
                    if current_interval > 0:
                        mock_schedule.every(current_interval).minutes.do(sqlscript.smart_transfer_attendance_data)

                # Verify schedule operations
                mock_schedule.clear.assert_called_once()
                mock_schedule.every.assert_called_with(5)


class TestIntegrationWithExistingCode:
    """Test integration of business hours scheduling with existing code."""

    def test_global_scheduler_creation(self, mock_env_vars):
        """Test that global scheduler is created properly."""
        with patch.dict(os.environ, mock_env_vars, clear=True):
            # Import should create global scheduler
            import importlib
            importlib.reload(sqlscript)

            # Verify scheduler exists and is properly configured
            assert hasattr(sqlscript, 'scheduler')
            assert isinstance(sqlscript.scheduler, sqlscript.BusinessHoursScheduler)
            assert sqlscript.scheduler.config is not None

    def test_backward_compatibility_with_existing_functions(self, mock_env_vars):
        """Test that existing functions still work with new scheduling."""
        with patch.dict(os.environ, mock_env_vars, clear=True):
            with patch('sqlscript.create_source_connection') as mock_source:
                with patch('sqlscript.create_orangehrm_connection') as mock_orange:
                    # Mock connections
                    mock_source.return_value = Mock()
                    mock_orange.return_value = Mock()

                    # Test that old transfer function still works
                    try:
                        sqlscript.transfer_attendance_data()
                        # Should not raise any errors
                    except Exception as e:
                        # Only database connection errors are expected in test environment
                        assert "connection" in str(e).lower() or "database" in str(e).lower()
