"""
Edge cases and error handling tests for business hours scheduling.
"""
import pytest
import os
import sys
from unittest.mock import patch, Mock
from datetime import datetime, timedelta
import pytz

# Add the parent directory to the path to import sqlscript
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import sqlscript


class TestBusinessHoursEdgeCases:
    """Test edge cases for business hours scheduling."""
    
    def test_timezone_handling_with_naive_datetime(self, mock_env_vars):
        """Test business hours with naive datetime objects."""
        env_vars = mock_env_vars.copy()
        env_vars.update({
            'BUSINESS_HOURS_ENABLED': 'true',
            'BUSINESS_START_HOUR': '9',
            'BUSINESS_START_MINUTE': '0',
            'BUSINESS_END_HOUR': '17',
            'BUSINESS_END_MINUTE': '0',
            'BUSINESS_DAYS': '1,2,3,4,5'
        })
        
        with patch.dict(os.environ, env_vars, clear=True):
            config = sqlscript.AttendanceConfig()
            scheduler = sqlscript.BusinessHoursScheduler(config)
            
            # Test with naive datetime (should be treated as local timezone)
            naive_dt = datetime(2024, 7, 29, 10, 0)  # Monday 10:00 AM
            result = scheduler.is_business_hours(naive_dt)
            assert result is True
    
    def test_timezone_handling_with_aware_datetime(self, mock_env_vars):
        """Test business hours with timezone-aware datetime objects."""
        env_vars = mock_env_vars.copy()
        env_vars.update({
            'BUSINESS_HOURS_ENABLED': 'true',
            'BUSINESS_START_HOUR': '9',
            'BUSINESS_START_MINUTE': '0',
            'BUSINESS_END_HOUR': '17',
            'BUSINESS_END_MINUTE': '0',
            'BUSINESS_DAYS': '1,2,3,4,5',
            'DEFAULT_TIMEZONE': 'Pacific/Guadalcanal'
        })
        
        with patch.dict(os.environ, env_vars, clear=True):
            config = sqlscript.AttendanceConfig()
            scheduler = sqlscript.BusinessHoursScheduler(config)
            
            # Test with timezone-aware datetime
            tz = pytz.timezone('Pacific/Guadalcanal')
            aware_dt = tz.localize(datetime(2024, 7, 29, 10, 0))  # Monday 10:00 AM
            result = scheduler.is_business_hours(aware_dt)
            assert result is True
    
    def test_cross_timezone_business_hours(self, mock_env_vars):
        """Test business hours with different timezones."""
        env_vars = mock_env_vars.copy()
        env_vars.update({
            'BUSINESS_HOURS_ENABLED': 'true',
            'BUSINESS_START_HOUR': '9',
            'BUSINESS_START_MINUTE': '0',
            'BUSINESS_END_HOUR': '17',
            'BUSINESS_END_MINUTE': '0',
            'BUSINESS_DAYS': '1,2,3,4,5',
            'DEFAULT_TIMEZONE': 'Pacific/Guadalcanal'
        })
        
        with patch.dict(os.environ, env_vars, clear=True):
            config = sqlscript.AttendanceConfig()
            scheduler = sqlscript.BusinessHoursScheduler(config)
            
            # Create datetime in UTC and convert to local timezone
            utc_tz = pytz.UTC
            local_tz = pytz.timezone('Pacific/Guadalcanal')
            
            # 10:00 AM in Guadalcanal time
            utc_dt = utc_tz.localize(datetime(2024, 7, 28, 23, 0))  # Previous day in UTC
            local_dt = utc_dt.astimezone(local_tz)
            
            result = scheduler.is_business_hours(local_dt)
            assert result is True
    
    def test_daylight_saving_time_handling(self, mock_env_vars):
        """Test business hours during daylight saving time transitions."""
        env_vars = mock_env_vars.copy()
        env_vars.update({
            'BUSINESS_HOURS_ENABLED': 'true',
            'BUSINESS_START_HOUR': '9',
            'BUSINESS_START_MINUTE': '0',
            'BUSINESS_END_HOUR': '17',
            'BUSINESS_END_MINUTE': '0',
            'BUSINESS_DAYS': '1,2,3,4,5',
            'DEFAULT_TIMEZONE': 'US/Eastern'  # Has DST
        })
        
        with patch.dict(os.environ, env_vars, clear=True):
            config = sqlscript.AttendanceConfig()
            scheduler = sqlscript.BusinessHoursScheduler(config)
            
            tz = pytz.timezone('US/Eastern')
            
            # Test during standard time
            std_time = tz.localize(datetime(2024, 1, 15, 10, 0))  # January (standard time)
            assert scheduler.is_business_hours(std_time) is True
            
            # Test during daylight saving time
            dst_time = tz.localize(datetime(2024, 7, 15, 10, 0))  # July (daylight time)
            assert scheduler.is_business_hours(dst_time) is True
    
    def test_weekend_edge_cases(self, mock_env_vars):
        """Test weekend edge cases."""
        env_vars = mock_env_vars.copy()
        env_vars.update({
            'BUSINESS_HOURS_ENABLED': 'true',
            'BUSINESS_START_HOUR': '9',
            'BUSINESS_START_MINUTE': '0',
            'BUSINESS_END_HOUR': '17',
            'BUSINESS_END_MINUTE': '0',
            'BUSINESS_DAYS': '1,2,3,4,5'  # Mon-Fri only
        })
        
        with patch.dict(os.environ, env_vars, clear=True):
            config = sqlscript.AttendanceConfig()
            scheduler = sqlscript.BusinessHoursScheduler(config)
            
            tz = pytz.timezone('Pacific/Guadalcanal')
            
            # Friday end of business
            friday_end = tz.localize(datetime(2024, 8, 2, 17, 0))  # Friday 5:00 PM
            assert scheduler.is_business_hours(friday_end) is True
            
            # Saturday during "business hours"
            saturday = tz.localize(datetime(2024, 8, 3, 10, 0))  # Saturday 10:00 AM
            assert scheduler.is_business_hours(saturday) is False
            
            # Monday start of business
            monday_start = tz.localize(datetime(2024, 8, 5, 9, 0))  # Monday 9:00 AM
            assert scheduler.is_business_hours(monday_start) is True
    
    def test_custom_business_days_configuration(self, mock_env_vars):
        """Test custom business days configuration."""
        # Test Tuesday-Saturday schedule
        env_vars = mock_env_vars.copy()
        env_vars.update({
            'BUSINESS_HOURS_ENABLED': 'true',
            'BUSINESS_START_HOUR': '10',
            'BUSINESS_START_MINUTE': '0',
            'BUSINESS_END_HOUR': '18',
            'BUSINESS_END_MINUTE': '0',
            'BUSINESS_DAYS': '2,3,4,5,6'  # Tue-Sat
        })
        
        with patch.dict(os.environ, env_vars, clear=True):
            config = sqlscript.AttendanceConfig()
            scheduler = sqlscript.BusinessHoursScheduler(config)
            
            tz = pytz.timezone('Pacific/Guadalcanal')
            
            # Monday should not be business day
            monday = tz.localize(datetime(2024, 7, 29, 12, 0))  # Monday
            assert scheduler.is_business_hours(monday) is False
            
            # Tuesday should be business day
            tuesday = tz.localize(datetime(2024, 7, 30, 12, 0))  # Tuesday
            assert scheduler.is_business_hours(tuesday) is True
            
            # Saturday should be business day
            saturday = tz.localize(datetime(2024, 8, 3, 12, 0))  # Saturday
            assert scheduler.is_business_hours(saturday) is True
            
            # Sunday should not be business day
            sunday = tz.localize(datetime(2024, 8, 4, 12, 0))  # Sunday
            assert scheduler.is_business_hours(sunday) is False
    
    def test_midnight_boundary_cases(self, mock_env_vars):
        """Test business hours around midnight."""
        # Test night shift schedule (e.g., 22:00 to 06:00)
        env_vars = mock_env_vars.copy()
        env_vars.update({
            'BUSINESS_HOURS_ENABLED': 'true',
            'BUSINESS_START_HOUR': '22',
            'BUSINESS_START_MINUTE': '0',
            'BUSINESS_END_HOUR': '6',
            'BUSINESS_END_MINUTE': '0',
            'BUSINESS_DAYS': '1,2,3,4,5'
        })
        
        with patch.dict(os.environ, env_vars, clear=True):
            config = sqlscript.AttendanceConfig()
            scheduler = sqlscript.BusinessHoursScheduler(config)
            
            tz = pytz.timezone('Pacific/Guadalcanal')
            
            # Note: This test assumes the business hours don't cross midnight
            # The current implementation treats start > end as invalid
            # This is a limitation that could be addressed in future versions
            
            # 11:00 PM should be business hours
            night_time = tz.localize(datetime(2024, 7, 29, 23, 0))
            result = scheduler.is_business_hours(night_time)
            # This will be False because end_hour < start_hour creates an invalid range
            assert result is False
    
    def test_zero_minute_intervals(self, mock_env_vars):
        """Test handling of zero-minute intervals."""
        env_vars = mock_env_vars.copy()
        env_vars.update({
            'BUSINESS_HOURS_ENABLED': 'true',
            'BUSINESS_HOURS_INTERVAL': '5',
            'OFF_HOURS_INTERVAL': '0'  # Disabled
        })
        
        with patch.dict(os.environ, env_vars, clear=True):
            config = sqlscript.AttendanceConfig()
            scheduler = sqlscript.BusinessHoursScheduler(config)
            
            # During business hours
            with patch.object(scheduler, 'is_business_hours', return_value=True):
                assert scheduler.get_current_interval() == 5
            
            # During off-hours with zero interval
            with patch.object(scheduler, 'is_business_hours', return_value=False):
                assert scheduler.get_current_interval() == 0
    
    def test_status_message_logging_changes(self, mock_env_vars):
        """Test that status messages are logged only when they change."""
        with patch.dict(os.environ, mock_env_vars, clear=True):
            config = sqlscript.AttendanceConfig()
            scheduler = sqlscript.BusinessHoursScheduler(config)
            
            with patch('sqlscript.logging.info') as mock_log:
                # First call should log
                scheduler.log_status_if_changed()
                assert mock_log.called
                
                # Reset mock
                mock_log.reset_mock()
                
                # Second call with same status should not log
                scheduler.log_status_if_changed()
                assert not mock_log.called
                
                # Change the status and call again - should log
                with patch.object(scheduler, 'get_status_message', return_value="Different status"):
                    scheduler.log_status_if_changed()
                    assert mock_log.called
    
    def test_error_handling_components(self, mock_env_vars):
        """Test error handling components exist."""
        with patch.dict(os.environ, mock_env_vars, clear=True):
            # Test that error handling exists in the function
            import inspect
            source = inspect.getsource(sqlscript.run_smart_scheduler)

            # Verify error handling patterns exist
            assert 'try:' in source
            assert 'except' in source
            assert 'KeyboardInterrupt' in source
            assert 'Exception' in source
