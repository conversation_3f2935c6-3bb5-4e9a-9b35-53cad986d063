# Environment Variables for Attendance Transfer Script
# Copy this file to .env and fill in your actual values

# SQL Server Database Configuration (Source)
SOURCE_DB_DRIVER=ODBC Driver 17 for SQL Server
SOURCE_DB_SERVER=BIOMETRIC-01\SQLSERVER2019
SOURCE_DB_DATABASE=att_db
SOURCE_DB_USERNAME=att_admin
SOURCE_DB_PASSWORD=your_sql_server_password_here
SOURCE_DB_TRUSTED_CONNECTION=yes
SOURCE_DB_ENCRYPT=false

# OrangeHRM MariaDB Database Configuration (Destination)
ORANGEHRM_DB_HOST=orangehrm.telekom.net.sb
ORANGEHRM_DB_PORT=3306
ORANGEHRM_DB_USER=ohrmadmin
ORANGEHRM_DB_PASSWORD=your_mariadb_password_here
ORANGEHRM_DB_DATABASE=orangehrmdb

# Application Configuration
DEFAULT_TIMEZONE=Pacific/Guadalcanal

# Business Hours Scheduling Configuration (Optional)
# Set BUSINESS_HOURS_ENABLED=false to run 24/7 with the same interval
BUSINESS_HOURS_ENABLED=true

# Business hours definition (24-hour format)
BUSINESS_START_HOUR=7
BUSINESS_START_MINUTE=30
BUSINESS_END_HOUR=17
BUSINESS_END_MINUTE=0

# Business days (1=Monday, 2=Tuesday, ..., 7=Sunday)
# Default: Monday to Friday (1,2,3,4,5)
BUSINESS_DAYS=1,2,3,4,5

# Scheduling intervals in minutes
BUSINESS_HOURS_INTERVAL=5    # Run every 5 minutes during business hours
OFF_HOURS_INTERVAL=60        # Run every 60 minutes during off-hours (set to 0 to disable)

# Logging Configuration (Optional)
LOG_LEVEL=INFO
LOG_FILE=attendance_transfer.log
