"""
Pytest configuration and fixtures for attendance transfer script tests.
"""
import pytest
import os
import tempfile
import shutil
from unittest.mock import Mock, MagicMock
from datetime import datetime
import pytz


@pytest.fixture
def mock_env_vars():
    """Fixture to provide mock environment variables."""
    return {
        'SOURCE_DB_DRIVER': 'ODBC Driver 17 for SQL Server',
        'SOURCE_DB_SERVER': 'test-server\\SQLSERVER',
        'SOURCE_DB_DATABASE': 'test_att_db',
        'SOURCE_DB_USERNAME': 'test_user',
        'SOURCE_DB_PASSWORD': 'test_password',
        'SOURCE_DB_TRUSTED_CONNECTION': 'yes',
        'SOURCE_DB_ENCRYPT': 'false',
        'ORANGEHRM_DB_HOST': 'test-mysql-host',
        'ORANGEHRM_DB_PORT': '3306',
        'ORANGEHRM_DB_USER': 'test_mysql_user',
        'OR<PERSON><PERSON>HRM_DB_PASSWORD': 'test_mysql_password',
        'ORANGEHRM_DB_DATABASE': 'test_orangehrm_db',
        'DEFAULT_TIMEZONE': 'Pacific/Guadalcanal',
        'LOG_LEVEL': 'DEBUG',
        'LOG_FILE': 'test_attendance.log'
    }


@pytest.fixture
def mock_sql_connection():
    """Fixture to provide a mock SQL Server connection."""
    mock_conn = Mock()
    mock_cursor = Mock()
    
    # Mock cursor methods
    mock_cursor.execute = Mock()
    mock_cursor.fetchone = Mock()
    mock_cursor.fetchall = Mock()
    mock_cursor.close = Mock()
    mock_cursor.description = [('AttID',), ('employeeID',), ('checkTime',), ('checkType',)]
    
    # Mock connection methods
    mock_conn.cursor = Mock(return_value=mock_cursor)
    mock_conn.close = Mock()
    
    return mock_conn, mock_cursor


@pytest.fixture
def mock_mysql_connection():
    """Fixture to provide a mock MySQL connection."""
    mock_conn = Mock()
    mock_cursor = Mock()
    
    # Mock cursor methods
    mock_cursor.execute = Mock()
    mock_cursor.fetchone = Mock()
    mock_cursor.fetchall = Mock()
    mock_cursor.close = Mock()
    mock_cursor.rowcount = 1
    
    # Mock connection methods
    mock_conn.cursor = Mock(return_value=mock_cursor)
    mock_conn.commit = Mock()
    mock_conn.rollback = Mock()
    mock_conn.close = Mock()
    
    return mock_conn, mock_cursor


@pytest.fixture
def sample_attendance_records():
    """Fixture to provide sample attendance records."""
    tz = pytz.timezone('Pacific/Guadalcanal')
    base_time = datetime(2025, 7, 28, 8, 0, 0)
    
    return [
        {
            'AttID': 1,
            'employeeID': 1001,
            'checkTime': tz.localize(base_time),
            'checkType': 'IN'
        },
        {
            'AttID': 2,
            'employeeID': 1001,
            'checkTime': tz.localize(base_time.replace(hour=17)),
            'checkType': 'OUT'
        },
        {
            'AttID': 3,
            'employeeID': 1002,
            'checkTime': tz.localize(base_time.replace(hour=9)),
            'checkType': 'IN'
        }
    ]


@pytest.fixture
def temp_env_file():
    """Fixture to create a temporary .env file for testing."""
    temp_dir = tempfile.mkdtemp()
    env_file = os.path.join(temp_dir, '.env')
    
    env_content = """
SOURCE_DB_SERVER=test-server
SOURCE_DB_DATABASE=test-db
SOURCE_DB_USERNAME=test_user
SOURCE_DB_PASSWORD=test_pass
ORANGEHRM_DB_HOST=test-host
ORANGEHRM_DB_USER=test_mysql_user
ORANGEHRM_DB_PASSWORD=test_mysql_pass
ORANGEHRM_DB_DATABASE=test_mysql_db
DEFAULT_TIMEZONE=UTC
"""
    
    with open(env_file, 'w') as f:
        f.write(env_content.strip())
    
    yield env_file
    
    # Cleanup
    shutil.rmtree(temp_dir)


@pytest.fixture(autouse=True)
def clean_env():
    """Fixture to clean environment variables before each test."""
    env_vars_to_clean = [
        'SOURCE_DB_DRIVER', 'SOURCE_DB_SERVER', 'SOURCE_DB_DATABASE',
        'SOURCE_DB_USERNAME', 'SOURCE_DB_PASSWORD', 'SOURCE_DB_TRUSTED_CONNECTION',
        'SOURCE_DB_ENCRYPT', 'ORANGEHRM_DB_HOST', 'ORANGEHRM_DB_PORT',
        'ORANGEHRM_DB_USER', 'ORANGEHRM_DB_PASSWORD', 'ORANGEHRM_DB_DATABASE',
        'DEFAULT_TIMEZONE', 'LOG_LEVEL', 'LOG_FILE',
        # Business hours scheduling variables
        'BUSINESS_HOURS_ENABLED', 'BUSINESS_START_HOUR', 'BUSINESS_START_MINUTE',
        'BUSINESS_END_HOUR', 'BUSINESS_END_MINUTE', 'BUSINESS_DAYS',
        'BUSINESS_HOURS_INTERVAL', 'OFF_HOURS_INTERVAL'
    ]
    
    # Store original values
    original_values = {}
    for var in env_vars_to_clean:
        original_values[var] = os.environ.get(var)
        if var in os.environ:
            del os.environ[var]
    
    yield
    
    # Restore original values
    for var, value in original_values.items():
        if value is not None:
            os.environ[var] = value
        elif var in os.environ:
            del os.environ[var]
