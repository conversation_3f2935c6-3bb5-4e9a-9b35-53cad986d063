"""
FastAPI monitoring interface for the attendance transfer system.
Provides REST API endpoints for monitoring, diagnostics, and manual operations.
"""

from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks, WebSocket, WebSocketDisconnect, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta
from contextlib import asynccontextmanager
import asyncio
import logging
import os
import json
from pathlib import Path

# Import your existing attendance transfer system
import sqlscript
from sqlscript import AttendanceTransferService, AttendanceConfig, BusinessHoursScheduler

# Pydantic models for API responses
class HealthStatus(BaseModel):
    status: str = Field(..., description="Overall system health status")
    timestamp: str = Field(..., description="Timestamp of health check")
    database_connections: Dict[str, str] = Field(..., description="Database connection statuses")
    performance_stats: Dict[str, Any] = Field(..., description="Performance statistics")
    validation_stats: Dict[str, Any] = Field(..., description="Validation statistics")
    issues: List[str] = Field(..., description="List of current issues")

class DiagnosticsResponse(BaseModel):
    timestamp: str = Field(..., description="Timestamp of diagnostics run")
    configuration: Dict[str, Any] = Field(..., description="System configuration")
    health_status: HealthStatus = Field(..., description="Current health status")
    recent_performance: Dict[str, Any] = Field(..., description="Recent performance metrics")
    validation_stats: Dict[str, Any] = Field(..., description="Validation statistics")
    recommendations: List[str] = Field(..., description="System recommendations")

class TransferResult(BaseModel):
    transferred_count: int = Field(..., description="Number of records transferred")
    duplicate_count: int = Field(..., description="Number of duplicate records found")
    error_count: int = Field(..., description="Number of errors encountered")
    timestamp: str = Field(..., description="Transfer completion timestamp")
    duration_seconds: float = Field(..., description="Transfer duration in seconds")

class SystemStatus(BaseModel):
    is_running: bool = Field(..., description="Whether the system is currently running")
    scheduler_status: str = Field(..., description="Current scheduler status")
    business_hours: bool = Field(..., description="Whether currently in business hours")
    next_run_time: Optional[str] = Field(None, description="Next scheduled run time")
    uptime: str = Field(..., description="System uptime")

class LogEntry(BaseModel):
    timestamp: str = Field(..., description="Log entry timestamp")
    level: str = Field(..., description="Log level")
    message: str = Field(..., description="Log message")

class ConfigurationUpdate(BaseModel):
    business_hours_enabled: Optional[bool] = None
    business_start_hour: Optional[int] = None
    business_start_minute: Optional[int] = None
    business_end_hour: Optional[int] = None
    business_end_minute: Optional[int] = None
    business_hours_interval: Optional[int] = None
    off_hours_interval: Optional[int] = None

# Global service instance and WebSocket manager
service = None
start_time = datetime.now()
websocket_manager = None

class ConnectionManager:
    """Manages WebSocket connections for real-time updates"""

    def __init__(self):
        self.active_connections: Set[WebSocket] = set()

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.add(websocket)
        logging.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        self.active_connections.discard(websocket)
        logging.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")

    async def send_personal_message(self, message: dict, websocket: WebSocket):
        try:
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            logging.error(f"Error sending personal message: {e}")
            self.disconnect(websocket)

    async def broadcast(self, message: dict):
        """Broadcast message to all connected clients"""
        if not self.active_connections:
            return

        disconnected = set()
        for connection in self.active_connections:
            try:
                await connection.send_text(json.dumps(message))
            except Exception as e:
                logging.error(f"Error broadcasting to connection: {e}")
                disconnected.add(connection)

        # Remove disconnected connections
        for connection in disconnected:
            self.disconnect(connection)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Initialize the service on startup"""
    try:
        global service, websocket_manager
        service = AttendanceTransferService()
        websocket_manager = ConnectionManager()
        logging.info("FastAPI monitoring server started successfully")

        # Start background monitoring task
        asyncio.create_task(background_monitoring_task())

    except Exception as e:
        logging.error(f"Failed to start monitoring server: {e}")
    yield

# Initialize FastAPI app
app = FastAPI(
    title="Attendance Transfer Monitor",
    description="Monitoring and management interface for the biometric attendance transfer system",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    lifespan=lifespan
)

# Add CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # React/Vite dev servers
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)



# Dependency injection functions
def get_service() -> AttendanceTransferService:
    """Dependency injection for AttendanceTransferService"""
    global service
    if service is None:
        try:
            service = AttendanceTransferService()
        except Exception as e:
            logging.error(f"Failed to initialize AttendanceTransferService: {e}")
            raise HTTPException(status_code=500, detail=f"Service initialization failed: {e}")
    return service

def get_websocket_manager() -> ConnectionManager:
    """Dependency injection for WebSocket manager"""
    global websocket_manager
    if websocket_manager is None:
        websocket_manager = ConnectionManager()
    return websocket_manager

# Background task functions
async def background_monitoring_task():
    """Background task that monitors system and sends updates via WebSocket"""
    while True:
        try:
            if websocket_manager and websocket_manager.active_connections:
                # Get current health status
                current_service = get_service()
                health_data = current_service.get_health_status()

                # Broadcast health update
                await websocket_manager.broadcast({
                    "type": "health_update",
                    "payload": health_data,
                    "timestamp": datetime.now().isoformat()
                })

            # Wait 30 seconds before next update
            await asyncio.sleep(30)

        except Exception as e:
            logging.error(f"Error in background monitoring task: {e}")
            await asyncio.sleep(60)  # Wait longer on error

async def run_transfer_in_background(transfer_id: str):
    """Run attendance transfer in background and notify via WebSocket"""
    try:
        logging.info(f"Starting background transfer {transfer_id}")

        # Notify start
        if websocket_manager:
            await websocket_manager.broadcast({
                "type": "transfer_started",
                "payload": {"transfer_id": transfer_id},
                "timestamp": datetime.now().isoformat()
            })

        # Run the transfer
        current_service = get_service()
        start_time = datetime.now()
        transferred, duplicates, errors = current_service.transfer_attendance_data()
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        # Prepare result
        result = {
            "transfer_id": transfer_id,
            "transferred_count": transferred,
            "duplicate_count": duplicates,
            "error_count": errors,
            "timestamp": end_time.isoformat(),
            "duration_seconds": duration
        }

        # Notify completion
        if websocket_manager:
            await websocket_manager.broadcast({
                "type": "transfer_complete",
                "payload": result,
                "timestamp": datetime.now().isoformat()
            })

        logging.info(f"Background transfer {transfer_id} completed: {transferred} transferred, {duplicates} duplicates, {errors} errors")

    except Exception as e:
        logging.error(f"Error in background transfer {transfer_id}: {e}")

        # Notify error
        if websocket_manager:
            await websocket_manager.broadcast({
                "type": "transfer_error",
                "payload": {
                    "transfer_id": transfer_id,
                    "error": str(e)
                },
                "timestamp": datetime.now().isoformat()
            })



# WebSocket endpoint
@app.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    manager: ConnectionManager = Depends(get_websocket_manager)
):
    """WebSocket endpoint for real-time updates"""
    await manager.connect(websocket)
    try:
        # Send initial health status
        service = get_service()
        health_data = service.get_health_status()
        await manager.send_personal_message({
            "type": "initial_health",
            "payload": health_data,
            "timestamp": datetime.now().isoformat()
        }, websocket)

        # Keep connection alive and handle incoming messages
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)

            # Handle different message types
            if message.get("type") == "ping":
                await manager.send_personal_message({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                }, websocket)
            elif message.get("type") == "request_health":
                health_data = service.get_health_status()
                await manager.send_personal_message({
                    "type": "health_update",
                    "payload": health_data,
                    "timestamp": datetime.now().isoformat()
                }, websocket)

    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logging.error(f"WebSocket error: {e}")
        manager.disconnect(websocket)

# API Routes with dependency injection
@app.get("/api/health", response_model=HealthStatus)
async def get_health_status(service: AttendanceTransferService = Depends(get_service)):
    """Get comprehensive system health status"""
    try:
        health_data = service.get_health_status()
        return HealthStatus(**health_data)
    except Exception as e:
        logging.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {e}")

@app.get("/api/diagnostics", response_model=DiagnosticsResponse)
async def get_diagnostics(service: AttendanceTransferService = Depends(get_service)):
    """Run comprehensive system diagnostics"""
    try:
        diagnostics_data = service.run_diagnostics()
        return DiagnosticsResponse(**diagnostics_data)
    except Exception as e:
        logging.error(f"Diagnostics failed: {e}")
        raise HTTPException(status_code=500, detail=f"Diagnostics failed: {e}")

@app.get("/api/status", response_model=SystemStatus)
async def get_system_status():
    """Get current system status and scheduler information"""
    try:
        scheduler = sqlscript.scheduler
        uptime = datetime.now() - start_time

        return SystemStatus(
            is_running=True,  # If API is responding, system is running
            scheduler_status=scheduler.get_status_message(),
            business_hours=scheduler.is_business_hours(),
            next_run_time=None,  # Would need to integrate with schedule library
            uptime=str(uptime).split('.')[0]  # Remove microseconds
        )
    except Exception as e:
        logging.error(f"Status check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Status check failed: {e}")

@app.post("/api/transfer/manual", response_model=TransferResult)
async def manual_transfer(
    service: AttendanceTransferService = Depends(get_service),
    manager: ConnectionManager = Depends(get_websocket_manager)
):
    """Manually trigger an attendance data transfer (synchronous)"""
    try:
        start_time_local = datetime.now()

        transferred, duplicates, errors = service.transfer_attendance_data()

        end_time = datetime.now()
        duration = (end_time - start_time_local).total_seconds()

        result = TransferResult(
            transferred_count=transferred,
            duplicate_count=duplicates,
            error_count=errors,
            timestamp=end_time.isoformat(),
            duration_seconds=duration
        )

        # Broadcast result to WebSocket clients
        await manager.broadcast({
            "type": "transfer_complete",
            "payload": result.dict(),
            "timestamp": datetime.now().isoformat()
        })

        return result
    except Exception as e:
        logging.error(f"Manual transfer failed: {e}")
        raise HTTPException(status_code=500, detail=f"Manual transfer failed: {e}")

@app.post("/api/transfer/background")
async def background_transfer(
    background_tasks: BackgroundTasks,
    manager: ConnectionManager = Depends(get_websocket_manager)
):
    """Trigger an attendance data transfer in the background"""
    try:
        transfer_id = f"bg_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Add background task
        background_tasks.add_task(run_transfer_in_background, transfer_id)

        return {
            "message": "Background transfer started",
            "transfer_id": transfer_id,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logging.error(f"Background transfer failed to start: {e}")
        raise HTTPException(status_code=500, detail=f"Background transfer failed to start: {e}")

@app.get("/api/logs")
async def get_recent_logs(lines: int = 100, level: Optional[str] = None):
    """Get recent log entries"""
    try:
        log_file = os.getenv('LOG_FILE', 'attendance_transfer.log')

        if not os.path.exists(log_file):
            return {"logs": [], "message": "Log file not found"}

        # Read last N lines from log file
        with open(log_file, 'r') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines

        # Parse log entries (basic parsing)
        logs = []
        for line in recent_lines:
            line = line.strip()
            if line:
                # Basic log parsing - adjust based on your log format
                parts = line.split(' - ', 2)
                if len(parts) >= 3:
                    timestamp, log_level, message = parts[0], parts[1], parts[2]
                    if level is None or log_level.upper() == level.upper():
                        logs.append({
                            "timestamp": timestamp,
                            "level": log_level,
                            "message": message
                        })

        return {"logs": logs, "total": len(logs)}
    except Exception as e:
        logging.error(f"Failed to read logs: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to read logs: {e}")

@app.get("/api/config")
async def get_configuration():
    """Get current system configuration (read-only)"""
    try:
        config = sqlscript.config
        scheduler = sqlscript.scheduler

        return {
            "database": {
                "source_server": config.source_db_config.get('server', 'Not configured'),
                "source_database": config.source_db_config.get('database', 'Not configured'),
                "orangehrm_host": config.orangehrm_db_config.get('host', 'Not configured'),
                "orangehrm_database": config.orangehrm_db_config.get('database', 'Not configured'),
            },
            "scheduling": {
                "business_hours_enabled": config.business_hours_enabled,
                "business_start_hour": config.business_start_hour,
                "business_start_minute": config.business_start_minute,
                "business_end_hour": config.business_end_hour,
                "business_end_minute": config.business_end_minute,
                "business_hours_interval": config.business_hours_interval,
                "off_hours_interval": config.off_hours_interval,
                "business_days": config.business_days,
            },
            "system": {
                "default_timezone": config.default_timezone,
                "log_level": os.getenv('LOG_LEVEL', 'INFO'),
                "log_file": os.getenv('LOG_FILE', 'attendance_transfer.log'),
            },
            "websocket": {
                "active_connections": len(websocket_manager.active_connections) if websocket_manager else 0,
                "endpoint": "/ws"
            }
        }
    except Exception as e:
        logging.error(f"Failed to get configuration: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get configuration: {e}")

@app.get("/api/websocket/status")
async def get_websocket_status(manager: ConnectionManager = Depends(get_websocket_manager)):
    """Get WebSocket connection status"""
    return {
        "active_connections": len(manager.active_connections),
        "endpoint": "/ws",
        "status": "active" if manager.active_connections else "no_connections"
    }

# Serve static files for the frontend
static_dir = Path("frontend/dist")
if static_dir.exists():
    # Only mount directories that exist
    static_static_dir = static_dir / "static"
    assets_dir = static_dir / "assets"

    if static_static_dir.exists():
        app.mount("/static", StaticFiles(directory="frontend/dist/static"), name="static")
    if assets_dir.exists():
        app.mount("/assets", StaticFiles(directory="frontend/dist/assets"), name="assets")

    @app.get("/")
    async def serve_frontend():
        return FileResponse("frontend/dist/index.html")

    @app.get("/{path:path}")
    async def serve_spa(path: str):
        """Serve SPA for all non-API routes"""
        if path.startswith("api/"):
            raise HTTPException(status_code=404, detail="API endpoint not found")

        file_path = static_dir / path
        if file_path.exists() and file_path.is_file():
            return FileResponse(file_path)

        # Fallback to index.html for SPA routing
        return FileResponse("frontend/dist/index.html")
else:
    @app.get("/")
    async def no_frontend():
        return {
            "message": "Frontend not built yet",
            "instructions": "Run 'npm run build' in the frontend directory",
            "api_docs": "/api/docs"
        }

if __name__ == "__main__":
    import uvicorn
    
    # Check if environment variables are set
    if not sqlscript.check_required_env_vars():
        print("❌ Required environment variables are missing. Please check your .env file.")
        exit(1)
    
    print("🚀 Starting Attendance Transfer Monitor API...")
    print("📊 API Documentation: http://localhost:8000/api/docs")
    print("🔍 Health Check: http://localhost:8000/api/health")
    
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
