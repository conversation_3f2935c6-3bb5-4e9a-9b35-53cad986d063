import schedule
import time
import mariadb
from datetime import datetime, timedelta
import pytz
import logging
import pyodbc
import os
from dotenv import load_dotenv
from contextlib import contextmanager

# Load environment variables from .env file
load_dotenv()

# Custom exceptions for better error handling
class AttendanceTransferError(Exception):
    """Base exception for attendance transfer errors"""
    pass

class DatabaseConnectionError(AttendanceTransferError):
    """Exception raised for database connection errors"""
    pass

class DataValidationError(AttendanceTransferError):
    """Exception raised for data validation errors"""
    pass

class ConfigurationError(AttendanceTransferError):
    """Exception raised for configuration errors"""
    pass

# Check for required environment variables
def check_required_env_vars():
    """
    Validate that all required environment variables are set.

    Returns:
        bool: True if all required variables are set, False otherwise
    """
    required_vars = [
        'SOURCE_DB_SERVER', 'SOURCE_DB_DATABASE', 'SOURCE_DB_USERNAME', 'SOURCE_DB_PASSWORD',
        'ORANGEHRM_DB_HOST', 'ORANGEHRM_DB_USER', 'ORANGEHRM_DB_PASSWORD', 'ORANGEHRM_DB_DATABASE'
    ]
    missing_vars = [var for var in required_vars if not os.getenv(var)]

    if missing_vars:
        logging.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logging.error("Please create a .env file with the required variables (see .env.example)")
        return False
    return True

class AttendanceConfig:
    """Configuration manager for attendance transfer system"""

    def __init__(self):
        """Initialize configuration from environment variables"""
        self.setup_logging()
        self.source_db_config = self._get_source_db_config()
        self.orangehrm_db_config = self._get_orangehrm_db_config()
        self.default_timezone = os.getenv('DEFAULT_TIMEZONE', 'Pacific/Guadalcanal')

        # Business hours configuration
        self.business_hours_enabled = os.getenv('BUSINESS_HOURS_ENABLED', 'true').lower() == 'true'
        self.business_start_hour = int(os.getenv('BUSINESS_START_HOUR', '7'))  # 7:30 AM (7 + 30 minutes)
        self.business_start_minute = int(os.getenv('BUSINESS_START_MINUTE', '30'))
        self.business_end_hour = int(os.getenv('BUSINESS_END_HOUR', '17'))  # 5:00 PM
        self.business_end_minute = int(os.getenv('BUSINESS_END_MINUTE', '0'))
        self.business_days = [int(d) for d in os.getenv('BUSINESS_DAYS', '1,2,3,4,5').split(',')]  # Mon-Fri (1=Monday, 7=Sunday)

        # Scheduling intervals
        self.business_hours_interval = int(os.getenv('BUSINESS_HOURS_INTERVAL', '5'))  # minutes
        self.off_hours_interval = int(os.getenv('OFF_HOURS_INTERVAL', '60'))  # minutes

        self.validate_config()

    def setup_logging(self):
        """Configure logging based on environment variables"""
        log_level = getattr(logging, os.getenv('LOG_LEVEL', 'INFO').upper())
        log_file = os.getenv('LOG_FILE', 'attendance_transfer.log')

        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )

    def _get_source_db_config(self):
        """Get source database configuration"""
        return {
            'Driver': os.getenv('SOURCE_DB_DRIVER', 'ODBC Driver 17 for SQL Server'),
            'server': os.getenv('SOURCE_DB_SERVER'),
            'database': os.getenv('SOURCE_DB_DATABASE'),
            'username': os.getenv('SOURCE_DB_USERNAME'),
            'password': os.getenv('SOURCE_DB_PASSWORD'),
            'trusted_connection': os.getenv('SOURCE_DB_TRUSTED_CONNECTION', 'yes'),
            'encrypt': os.getenv('SOURCE_DB_ENCRYPT', 'false')
        }

    def _get_orangehrm_db_config(self):
        """Get OrangeHRM database configuration"""
        return {
            'host': os.getenv('ORANGEHRM_DB_HOST'),
            'port': int(os.getenv('ORANGEHRM_DB_PORT', '3306')),
            'user': os.getenv('ORANGEHRM_DB_USER'),
            'password': os.getenv('ORANGEHRM_DB_PASSWORD'),
            'database': os.getenv('ORANGEHRM_DB_DATABASE')
        }

    def validate_config(self):
        """Validate configuration settings"""
        try:
            pytz.timezone(self.default_timezone)
        except pytz.UnknownTimeZoneError:
            raise ConfigurationError(f"Invalid timezone: {self.default_timezone}")

        # Validate business hours configuration
        if self.business_hours_enabled:
            if not (0 <= self.business_start_hour <= 23):
                raise ConfigurationError(f"Invalid business start hour: {self.business_start_hour}")
            if not (0 <= self.business_start_minute <= 59):
                raise ConfigurationError(f"Invalid business start minute: {self.business_start_minute}")
            if not (0 <= self.business_end_hour <= 23):
                raise ConfigurationError(f"Invalid business end hour: {self.business_end_hour}")
            if not (0 <= self.business_end_minute <= 59):
                raise ConfigurationError(f"Invalid business end minute: {self.business_end_minute}")
            if not all(1 <= day <= 7 for day in self.business_days):
                raise ConfigurationError(f"Invalid business days: {self.business_days}")
            if self.business_hours_interval < 1:
                raise ConfigurationError(f"Business hours interval must be at least 1 minute: {self.business_hours_interval}")
            if self.off_hours_interval < 0:
                raise ConfigurationError(f"Off hours interval must be at least 0 minutes: {self.off_hours_interval}")

class BusinessHoursScheduler:
    """Manages business hours scheduling logic"""

    def __init__(self, config):
        """Initialize with configuration"""
        self.config = config
        self.last_status_log = None

    def is_business_hours(self, dt=None):
        """
        Check if the given datetime (or current time) is within business hours

        Args:
            dt: datetime object to check (defaults to current time in configured timezone)

        Returns:
            bool: True if within business hours, False otherwise
        """
        if not self.config.business_hours_enabled:
            return True  # Always considered business hours if disabled

        if dt is None:
            tz = pytz.timezone(self.config.default_timezone)
            dt = datetime.now(tz)
        elif dt.tzinfo is None:
            # Assume naive datetime is in the configured timezone
            tz = pytz.timezone(self.config.default_timezone)
            dt = tz.localize(dt)

        # Check if it's a business day (1=Monday, 7=Sunday)
        weekday = dt.isoweekday()
        if weekday not in self.config.business_days:
            return False

        # Check if it's within business hours
        business_start = dt.replace(
            hour=self.config.business_start_hour,
            minute=self.config.business_start_minute,
            second=0,
            microsecond=0
        )
        business_end = dt.replace(
            hour=self.config.business_end_hour,
            minute=self.config.business_end_minute,
            second=0,
            microsecond=0
        )

        return business_start <= dt <= business_end

    def get_current_interval(self):
        """
        Get the appropriate scheduling interval based on current time

        Returns:
            int: Interval in minutes
        """
        if self.is_business_hours():
            return self.config.business_hours_interval
        else:
            return self.config.off_hours_interval

    def get_status_message(self):
        """
        Get a status message describing current scheduling mode

        Returns:
            str: Status message
        """
        if not self.config.business_hours_enabled:
            return f"24/7 mode - running every {self.config.business_hours_interval} minutes"

        if self.is_business_hours():
            return f"Business hours - running every {self.config.business_hours_interval} minutes"
        else:
            return f"Off hours - running every {self.config.off_hours_interval} minutes"

    def log_status_if_changed(self):
        """Log status message if it has changed since last log"""
        current_status = self.get_status_message()
        if current_status != self.last_status_log:
            logging.info(f"Scheduler status: {current_status}")
            if self.config.business_hours_enabled:
                tz = pytz.timezone(self.config.default_timezone)
                now = datetime.now(tz)
                business_start = f"{self.config.business_start_hour:02d}:{self.config.business_start_minute:02d}"
                business_end = f"{self.config.business_end_hour:02d}:{self.config.business_end_minute:02d}"
                business_days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                days_str = ', '.join([business_days[d-1] for d in sorted(self.config.business_days)])
                logging.info(f"Business hours: {business_start}-{business_end}, {days_str} ({self.config.default_timezone})")
                logging.info(f"Current time: {now.strftime('%Y-%m-%d %H:%M:%S %Z')}")
            self.last_status_log = current_status

# Create global configuration instance
config = AttendanceConfig()

# Create global scheduler instance
scheduler = BusinessHoursScheduler(config)

# Maintain backward compatibility
SOURCE_DB_CONFIG = config.source_db_config
ORANGEHRM_DB_CONFIG = config.orangehrm_db_config
DEFAULT_TIMEZONE = config.default_timezone

class DatabaseManager:
    """Manages database connections and operations"""

    def __init__(self, config, max_retries=3, retry_delay=1):
        """
        Initialize with configuration and retry settings

        Args:
            config: Configuration instance
            max_retries: Maximum number of connection retry attempts
            retry_delay: Delay between retry attempts in seconds
        """
        self.config = config
        self.max_retries = max_retries
        self.retry_delay = retry_delay

    @contextmanager
    def get_connections(self):
        """Context manager for database connections with retry logic"""
        source_conn = None
        orange_conn = None
        try:
            source_conn = self._create_connection_with_retry(self.create_source_connection, "SQL Server")
            orange_conn = self._create_connection_with_retry(self.create_orangehrm_connection, "MariaDB")

            if not source_conn or not orange_conn:
                raise DatabaseConnectionError("Failed to establish required database connections")

            yield source_conn, orange_conn
        finally:
            self._safe_close_connection(source_conn, "SQL Server")
            self._safe_close_connection(orange_conn, "MariaDB")

    def _create_connection_with_retry(self, connection_func, db_name):
        """
        Create database connection with retry logic

        Args:
            connection_func: Function to create the connection
            db_name: Name of the database for logging

        Returns:
            Database connection or None
        """
        import time

        for attempt in range(1, self.max_retries + 1):
            try:
                logging.info(f"Attempting to connect to {db_name} (attempt {attempt}/{self.max_retries})")
                connection = connection_func()
                if connection:
                    logging.info(f"Successfully connected to {db_name}")
                    return connection
            except Exception as e:
                logging.warning(f"Connection attempt {attempt} to {db_name} failed: {e}")

                if attempt < self.max_retries:
                    logging.info(f"Retrying in {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)
                else:
                    logging.error(f"All {self.max_retries} connection attempts to {db_name} failed")
                    raise DatabaseConnectionError(f"Failed to connect to {db_name} after {self.max_retries} attempts")

        return None

    def _safe_close_connection(self, connection, db_name):
        """Safely close database connection"""
        if connection:
            try:
                connection.close()
                logging.debug(f"Closed {db_name} connection")
            except Exception as e:
                logging.warning(f"Error closing {db_name} connection: {e}")

    def create_source_connection(self):
        """Create SQL Server connection using pyodbc"""
        try:
            conn_str = f"SERVER={self.config.source_db_config['server']};" \
                       f"DRIVER={self.config.source_db_config['Driver']};" \
                       f"DATABASE={self.config.source_db_config['database']};" \
                       f"UID={self.config.source_db_config['username']};" \
                       f"PWD={self.config.source_db_config['password']};" \
                       f"Trusted_Connection={self.config.source_db_config['trusted_connection']};" \
                       f"Encrypt={self.config.source_db_config['encrypt']};"

            conn = pyodbc.connect(conn_str)
            logging.info("SQL Server connection successful")
            return conn
        except pyodbc.Error as e:
            logging.error(f"Error connecting to SQL Server: {e}")
            raise DatabaseConnectionError(f"SQL Server connection failed: {e}")

    def create_orangehrm_connection(self):
        """Create MariaDB connection for OrangeHRM"""
        try:
            conn = mariadb.connect(
                host=self.config.orangehrm_db_config['host'],
                port=self.config.orangehrm_db_config['port'],
                user=self.config.orangehrm_db_config['user'],
                password=self.config.orangehrm_db_config['password'],
                database=self.config.orangehrm_db_config['database'],
            )
            logging.info("MariaDB connection successful")
            return conn
        except mariadb.Error as e:
            logging.error(f"Error connecting to MariaDB: {e}")
            raise DatabaseConnectionError(f"MariaDB connection failed: {e}")

# Maintain backward compatibility
def create_source_connection():
    """Create SQL Server connection using pyodbc"""
    try:
        conn_str = f"SERVER={SOURCE_DB_CONFIG['server']};" \
                   f"DRIVER={SOURCE_DB_CONFIG['Driver']};" \
                   f"DATABASE={SOURCE_DB_CONFIG['database']};" \
                   f"UID={SOURCE_DB_CONFIG['username']};" \
                   f"PWD={SOURCE_DB_CONFIG['password']};" \
                   f"Trusted_Connection={SOURCE_DB_CONFIG['trusted_connection']};" \
                   f"Encrypt={SOURCE_DB_CONFIG['encrypt']};"

        conn = pyodbc.connect(conn_str)
        logging.info("SQL Server connection successful")
        return conn
    except pyodbc.Error as e:
        logging.error(f"Error connecting to SQL Server: {e}")
        return None

def create_orangehrm_connection():
    """Create MariaDB connection for OrangeHRM"""
    try:
        conn = mariadb.connect(
            host=ORANGEHRM_DB_CONFIG['host'],
            port=ORANGEHRM_DB_CONFIG['port'],
            user=ORANGEHRM_DB_CONFIG['user'],
            password=ORANGEHRM_DB_CONFIG['password'],
            database=ORANGEHRM_DB_CONFIG['database'],
        )
        logging.info("MariaDB connection successful")
        return conn
    except mariadb.Error as e:
        logging.error(f"Error connecting to MariaDB: {e}")
        return None

class AttendanceTransferService:
    """Main service class for transferring attendance data"""

    def __init__(self, config=None):
        """Initialize the service with configuration"""
        self.config = config or AttendanceConfig()
        self.db_manager = DatabaseManager(self.config)
        self.data_processor = AttendanceDataProcessor(self.config)

    def get_attendance_records(self, conn):
        """
        Fetch today's attendance records from source database

        Args:
            conn: Database connection

        Returns:
            list: List of attendance records
        """
        try:
            # Get today's date in the default timezone
            tz = pytz.timezone(self.config.default_timezone)
            now_in_tz = datetime.now(tz)
            today = now_in_tz.date()

            # Calculate start and end of day in the default timezone
            start_of_day = datetime.combine(today, datetime.min.time())
            end_of_day = start_of_day + timedelta(days=1)

            # Format for SQL query (using local time)
            start_str = start_of_day.strftime('%Y-%m-%d %H:%M:%S')
            end_str = end_of_day.strftime('%Y-%m-%d %H:%M:%S')

            logging.info(f"Fetching records for {today} (Local range: {start_str} to {end_str})")

            # Debug: Check server time
            cursor = conn.cursor()
            cursor.execute("SELECT GETDATE()")
            server_time = cursor.fetchone()[0]
            logging.info(f"SQL Server current time: {server_time}")

            query = """
                SELECT AttID, employeeID, checkTime, checkType
                FROM dbo.attendance
                WHERE checkTime >= ? AND checkTime < ?
                ORDER BY checkTime
            """

            cursor.execute(query, (start_of_day, end_of_day))
            columns = [column[0] for column in cursor.description]
            records = [dict(zip(columns, row)) for row in cursor.fetchall()]
            logging.info(f"Fetched {len(records)} attendance records for today")

            # Log first 5 records if any exist
            if records:
                for i, rec in enumerate(records[:min(5, len(records))]):
                    logging.info(f"Record {i + 1}: ID={rec['AttID']}, EmpID={rec['employeeID']}, "
                                 f"Time={rec['checkTime']}, Type={rec['checkType']}")
            else:
                self._debug_no_records(cursor, start_of_day, end_of_day)

            return records

        except Exception as e:
            logging.error(f"Error fetching attendance records: {e}", exc_info=True)
            return []
        finally:
            cursor.close()

    def _debug_no_records(self, cursor, start_of_day, end_of_day):
        """Debug helper when no records are found"""
        # Debug: Check if any records exist at all
        cursor.execute("SELECT TOP 1 * FROM dbo.attendance ORDER BY AttID DESC")
        sample = cursor.fetchone()
        if sample:
            logging.info(f"Sample record exists: ID={sample[0]}, Time={sample[2]}")
        else:
            logging.warning("No records found in dbo.attendance table at all")

        # Debug: Check count in date range
        cursor.execute("SELECT COUNT(*) FROM dbo.attendance WHERE checkTime >= ? AND checkTime < ?",
                       (start_of_day, end_of_day))
        count = cursor.fetchone()[0]
        logging.info(f"Count of records in date range: {count}")

# Maintain backward compatibility
def get_attendance_records(conn):
    """Fetch today's attendance records without pairing"""
    service = AttendanceTransferService()
    return service.get_attendance_records(conn)

class AttendanceDataProcessor:
    """Handles processing and validation of attendance data"""

    def __init__(self, config):
        """Initialize with configuration"""
        self.config = config
        self.validation_stats = {
            'total_validated': 0,
            'validation_errors': 0,
            'time_conversion_errors': 0
        }

    def validate_employee_id(self, emp_id):
        """
        Validate employee ID format and value

        Args:
            emp_id: Employee ID to validate

        Returns:
            tuple: (is_valid, error_message)
        """
        self.validation_stats['total_validated'] += 1

        if emp_id is None:
            self.validation_stats['validation_errors'] += 1
            return False, "Employee ID is None"

        try:
            # Convert to int to validate it's numeric
            emp_id_int = int(emp_id)
            if emp_id_int <= 0:
                self.validation_stats['validation_errors'] += 1
                return False, "Employee ID must be positive"
            if emp_id_int > 9999:
                self.validation_stats['validation_errors'] += 1
                return False, "Employee ID exceeds maximum value (9999)"
            return True, None
        except (ValueError, TypeError):
            self.validation_stats['validation_errors'] += 1
            return False, "Employee ID must be numeric"

    def validate_check_time(self, check_time):
        """
        Validate check time value with comprehensive checks

        Args:
            check_time: Timestamp to validate

        Returns:
            tuple: (is_valid, error_message)
        """
        if check_time is None:
            return False, "Check time is None"

        if not isinstance(check_time, datetime):
            return False, "Check time must be a datetime object"

        # Check if time is reasonable (not too far in the past or future)
        from datetime import timedelta
        now = datetime.now()
        max_past = now - timedelta(days=7)  # Allow up to 7 days in the past
        max_future = now + timedelta(hours=1)  # Allow up to 1 hour in the future

        if check_time < max_past:
            return False, f"Check time is too far in the past (older than 7 days)"

        if check_time > max_future:
            return False, f"Check time is in the future"

        return True, None

    def validate_attendance_record(self, record):
        """
        Comprehensive validation of an attendance record

        Args:
            record: AttendanceRecord instance

        Returns:
            tuple: (is_valid, error_messages_list)
        """
        errors = []

        # Validate employee ID
        emp_valid, emp_error = self.validate_employee_id(record.employee_id)
        if not emp_valid:
            errors.append(f"Employee ID: {emp_error}")

        # Validate check time
        time_valid, time_error = self.validate_check_time(record.check_time)
        if not time_valid:
            errors.append(f"Check time: {time_error}")

        # Validate check type
        if not record.check_type or record.check_type.strip() == '':
            errors.append("Check type is empty")
        elif not (record.is_punch_in() or record.is_punch_out()):
            errors.append(f"Unknown check type: {record.check_type}")

        return len(errors) == 0, errors

    def convert_to_utc_and_offset(self, naive_dt):
        """
        Convert naive datetime to UTC and calculate offset using configured timezone

        Args:
            naive_dt: Naive datetime object

        Returns:
            tuple: (utc_datetime, offset_minutes)
        """
        try:
            tz = pytz.timezone(self.config.default_timezone)
        except pytz.UnknownTimeZoneError:
            logging.error(f"Invalid default timezone: {self.config.default_timezone}. Using UTC")
            tz = pytz.UTC

        try:
            # Ensure naive_dt is timezone-naive
            if naive_dt.tzinfo is not None:
                naive_dt = naive_dt.replace(tzinfo=None)

            localized_dt = tz.localize(naive_dt)
            utc_dt = localized_dt.astimezone(pytz.utc)
            offset = localized_dt.utcoffset().total_seconds() / 60
            return utc_dt.replace(tzinfo=None), int(offset)
        except Exception as e:
            logging.error(f"Time conversion error: {e}")
            return naive_dt, 0  # Fallback

    def format_employee_id(self, emp_id):
        """Format employee ID as 4-digit string"""
        return str(emp_id).zfill(4)

# Maintain backward compatibility
def validate_employee_id(emp_id):
    """Validate employee ID format and value"""
    processor = AttendanceDataProcessor(config)
    return processor.validate_employee_id(emp_id)

def convert_to_utc_and_offset(naive_dt):
    """Convert naive datetime to UTC and calculate offset using DEFAULT_TIMEZONE"""
    try:
        # Use DEFAULT_TIMEZONE for backward compatibility with tests
        tz = pytz.timezone(DEFAULT_TIMEZONE)
    except pytz.UnknownTimeZoneError:
        logging.error(f"Invalid default timezone: {DEFAULT_TIMEZONE}. Using UTC")
        tz = pytz.UTC

    try:
        # Ensure naive_dt is timezone-naive
        if naive_dt.tzinfo is not None:
            naive_dt = naive_dt.replace(tzinfo=None)

        localized_dt = tz.localize(naive_dt)
        utc_dt = localized_dt.astimezone(pytz.utc)
        offset = localized_dt.utcoffset().total_seconds() / 60
        return utc_dt.replace(tzinfo=None), int(offset)
    except Exception as e:
        logging.error(f"Time conversion error: {e}")
        return naive_dt, 0  # Fallback


class AttendanceRecord:
    """Data class for attendance record"""

    def __init__(self, record_data):
        """Initialize from database record"""
        self.id = record_data.get('AttID')
        self.employee_id = record_data.get('employeeID')
        self.check_time = record_data.get('checkTime')
        self.check_type = record_data.get('checkType', '').strip().upper() if record_data.get('checkType') else ''

        # Will be set during processing
        self.utc_time = None
        self.offset = None
        self.formatted_employee_id = None

    def is_punch_in(self):
        """Check if this is a punch-in record"""
        return self.check_type in ['IN', 'I']

    def is_punch_out(self):
        """Check if this is a punch-out record"""
        return self.check_type in ['OUT', 'O']

    def __str__(self):
        """String representation"""
        return f"Record ID={self.id}, EmpID={self.employee_id}, Time={self.check_time}, Type={self.check_type}"


class AttendanceTransferService(AttendanceTransferService):
    """Main service class for transferring attendance data"""

    def __init__(self, config=None, batch_size=100):
        """
        Initialize the service with configuration and batch size

        Args:
            config: Configuration instance
            batch_size: Number of records to process in each batch
        """
        super().__init__(config)
        self.batch_size = batch_size
        self.performance_stats = {
            'total_records': 0,
            'processing_time': 0,
            'batch_count': 0,
            'avg_batch_time': 0
        }

    def transfer_attendance_data(self):
        """
        Main function to transfer attendance data from source to OrangeHRM

        Returns:
            tuple: (transferred_count, duplicate_count, error_count)
        """
        import time
        start_time = time.time()

        try:
            with self.db_manager.get_connections() as (source_conn, orange_conn):
                attendance_records = self.get_attendance_records(source_conn)
                if not attendance_records:
                    logging.info("No attendance records found for today")
                    return 0, 0, 0

                self.performance_stats['total_records'] = len(attendance_records)
                result = self._process_records_in_batches(attendance_records, orange_conn)

                # Log performance statistics
                self.performance_stats['processing_time'] = time.time() - start_time
                self._log_performance_stats()

                return result
        except DatabaseConnectionError as e:
            logging.error(f"Database connection error: {e}")
            return 0, 0, 0
        except Exception as e:
            logging.error(f"Unexpected error in transfer process: {e}", exc_info=True)
            return 0, 0, 0

    def _process_records_in_batches(self, attendance_records, orange_conn):
        """
        Process attendance records in batches for better performance

        Args:
            attendance_records: List of attendance records
            orange_conn: Database connection

        Returns:
            tuple: (transferred_count, duplicate_count, error_count)
        """
        import time
        total_transferred = 0
        total_duplicates = 0
        total_errors = 0

        # Split records into batches
        batches = [attendance_records[i:i + self.batch_size]
                  for i in range(0, len(attendance_records), self.batch_size)]

        self.performance_stats['batch_count'] = len(batches)
        batch_times = []

        for batch_num, batch in enumerate(batches, 1):
            batch_start = time.time()

            try:
                logging.info(f"Processing batch {batch_num}/{len(batches)} ({len(batch)} records)")

                transferred, duplicates, errors = self._process_records(batch, orange_conn)

                total_transferred += transferred
                total_duplicates += duplicates
                total_errors += errors

                batch_time = time.time() - batch_start
                batch_times.append(batch_time)

                logging.info(f"Batch {batch_num} completed in {batch_time:.2f}s: "
                           f"Transferred: {transferred}, Duplicates: {duplicates}, Errors: {errors}")

                # Commit after each batch for better reliability
                orange_conn.commit()

            except Exception as e:
                logging.error(f"Error processing batch {batch_num}: {e}")
                orange_conn.rollback()
                total_errors += len(batch)  # Count all records in failed batch as errors

        # Calculate average batch time
        if batch_times:
            self.performance_stats['avg_batch_time'] = sum(batch_times) / len(batch_times)

        return total_transferred, total_duplicates, total_errors

    def _log_performance_stats(self):
        """Log performance statistics"""
        stats = self.performance_stats
        logging.info("=== Performance Statistics ===")
        logging.info(f"Total records processed: {stats['total_records']}")
        logging.info(f"Total processing time: {stats['processing_time']:.2f} seconds")
        logging.info(f"Records per second: {stats['total_records'] / max(stats['processing_time'], 0.001):.2f}")
        logging.info(f"Number of batches: {stats['batch_count']}")
        logging.info(f"Average batch time: {stats['avg_batch_time']:.2f} seconds")
        logging.info(f"Records per batch: {stats['total_records'] / max(stats['batch_count'], 1):.1f}")
        logging.info("==============================")

    def _process_records(self, attendance_records, orange_conn):
        """
        Process attendance records and transfer to OrangeHRM

        Args:
            attendance_records: List of attendance records to process
            orange_conn: Database connection

        Returns:
            tuple: (transferred_count, duplicate_count, error_count)
        """
        transferred = 0
        duplicates = 0
        errors = 0

        orange_cursor = orange_conn.cursor()

        try:
            # Pre-validate all records to fail fast
            validated_records = []
            for record_data in attendance_records:
                try:
                    record = AttendanceRecord(record_data)

                    # Validate employee ID
                    is_valid, error_msg = self.data_processor.validate_employee_id(record.employee_id)
                    if not is_valid:
                        logging.warning(f"Skipping record with invalid employeeID: ID={record.id}, Error: {error_msg}")
                        errors += 1
                        continue

                    # Format employee ID
                    record.formatted_employee_id = self.data_processor.format_employee_id(record.employee_id)

                    # Validate check time
                    if record.check_time is None:
                        logging.warning(f"Skipping record with null checkTime: ID={record.id}, EmpID={record.formatted_employee_id}")
                        errors += 1
                        continue

                    # Convert time to UTC and offset
                    record.utc_time, record.offset = self.data_processor.convert_to_utc_and_offset(record.check_time)

                    validated_records.append(record)

                except Exception as e:
                    errors += 1
                    logging.error(f"Error validating record ID={record_data.get('AttID')}: {e}")

            # Batch employee existence checks for better performance
            employee_cache = self._batch_check_employees(orange_cursor, validated_records)

            # Process validated records
            for record in validated_records:
                try:
                    # Check employee exists using cache
                    if not employee_cache.get(record.formatted_employee_id, False):
                        logging.warning(f"Employee {record.formatted_employee_id} not found in OrangeHRM")
                        continue

                    # Process based on check type
                    if record.is_punch_in():
                        result = self._process_punch_in(orange_cursor, record)
                    elif record.is_punch_out():
                        result = self._process_punch_out(orange_cursor, record)
                    else:
                        logging.warning(f"Unknown checkType '{record.check_type}' for record ID={record.id}")
                        errors += 1
                        continue

                    # Update counters based on result
                    if result == 'transferred':
                        transferred += 1
                    elif result == 'duplicate':
                        duplicates += 1
                    elif result == 'error':
                        errors += 1

                except Exception as e:
                    errors += 1
                    logging.error(f"Error processing record ID={record.id}: {e}")

            return transferred, duplicates, errors

        except Exception as e:
            logging.error(f"Database error during record processing: {e}")
            return 0, 0, 0
        finally:
            orange_cursor.close()

    def _batch_check_employees(self, cursor, records):
        """
        Batch check employee existence for better performance

        Args:
            cursor: Database cursor
            records: List of AttendanceRecord objects

        Returns:
            dict: Employee ID -> exists mapping
        """
        employee_cache = {}

        # Get unique employee IDs
        unique_employee_ids = list(set(record.formatted_employee_id for record in records))

        if not unique_employee_ids:
            return employee_cache

        try:
            # Create placeholders for IN clause
            placeholders = ','.join(['%s'] * len(unique_employee_ids))
            query = f"SELECT employee_id FROM hs_hr_employee WHERE employee_id IN ({placeholders})"

            cursor.execute(query, unique_employee_ids)
            existing_employees = {row[0] for row in cursor.fetchall()}

            # Build cache
            for emp_id in unique_employee_ids:
                employee_cache[emp_id] = emp_id in existing_employees

            logging.debug(f"Employee cache built: {len(existing_employees)}/{len(unique_employee_ids)} employees found")

        except Exception as e:
            logging.error(f"Error building employee cache: {e}")
            # Fallback to individual checks
            for emp_id in unique_employee_ids:
                employee_cache[emp_id] = self._employee_exists(cursor, emp_id)

        return employee_cache

    def _employee_exists(self, cursor, employee_id):
        """Check if employee exists in OrangeHRM"""
        emp_check = "SELECT employee_id FROM hs_hr_employee WHERE employee_id = %s"
        cursor.execute(emp_check, (employee_id,))
        return cursor.fetchone() is not None

    def get_insert_query(self):
        """Get the SQL query for inserting attendance records"""
        return """
               INSERT INTO ohrm_attendance_record (employee_id,
                                                   punch_in_user_time, punch_in_utc_time, punch_in_time_offset,
                                                   punch_in_timezone_name,
                                                   punch_out_user_time, punch_out_utc_time, punch_out_time_offset,
                                                   punch_out_timezone_name,
                                                   state, punch_in_note, punch_out_note)
               VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
               """

    def get_health_status(self):
        """
        Get health status of the attendance transfer system

        Returns:
            dict: Health status information
        """
        health_status = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'database_connections': {},
            'performance_stats': self.performance_stats.copy(),
            'validation_stats': self.data_processor.validation_stats.copy(),
            'issues': []
        }

        try:
            # Test database connections
            with self.db_manager.get_connections() as (source_conn, orange_conn):
                # Test source database
                try:
                    cursor = source_conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                    cursor.close()
                    health_status['database_connections']['source'] = 'healthy'
                except Exception as e:
                    health_status['database_connections']['source'] = f'error: {e}'
                    health_status['issues'].append(f"Source database connection issue: {e}")

                # Test OrangeHRM database
                try:
                    cursor = orange_conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                    cursor.close()
                    health_status['database_connections']['orangehrm'] = 'healthy'
                except Exception as e:
                    health_status['database_connections']['orangehrm'] = f'error: {e}'
                    health_status['issues'].append(f"OrangeHRM database connection issue: {e}")

        except Exception as e:
            health_status['database_connections']['overall'] = f'error: {e}'
            health_status['issues'].append(f"Database connection manager issue: {e}")

        # Check configuration
        try:
            # Test timezone configuration
            import pytz
            pytz.timezone(self.config.default_timezone)
        except Exception as e:
            health_status['issues'].append(f"Timezone configuration issue: {e}")

        # Determine overall status
        if health_status['issues']:
            health_status['status'] = 'degraded' if len(health_status['issues']) < 3 else 'unhealthy'

        return health_status

    def run_diagnostics(self):
        """
        Run comprehensive diagnostics on the system

        Returns:
            dict: Diagnostic information
        """
        diagnostics = {
            'timestamp': datetime.now().isoformat(),
            'configuration': {
                'batch_size': self.batch_size,
                'default_timezone': self.config.default_timezone,
                'max_retries': self.db_manager.max_retries,
                'retry_delay': self.db_manager.retry_delay
            },
            'health_status': self.get_health_status(),
            'recent_performance': self.performance_stats,
            'validation_stats': self.data_processor.validation_stats,
            'recommendations': []
        }

        # Add recommendations based on performance
        if self.performance_stats.get('avg_batch_time', 0) > 10:
            diagnostics['recommendations'].append("Consider reducing batch size for better performance")

        if self.data_processor.validation_stats.get('validation_errors', 0) > 0:
            error_rate = (self.data_processor.validation_stats['validation_errors'] /
                         max(self.data_processor.validation_stats['total_validated'], 1)) * 100
            if error_rate > 10:
                diagnostics['recommendations'].append(f"High validation error rate ({error_rate:.1f}%) - check data quality")

        return diagnostics

    def _process_punch_in(self, cursor, record):
        """
        Process a punch-in record with transaction safety

        Args:
            cursor: Database cursor
            record: AttendanceRecord instance

        Returns:
            str: 'transferred', 'duplicate', or 'error'
        """
        try:
            # Convert datetime objects to string in ISO format
            user_time_str = record.check_time.strftime('%Y-%m-%d %H:%M:%S')
            utc_time_str = record.utc_time.strftime('%Y-%m-%d %H:%M:%S')

            # Check for duplicate with more specific criteria
            dup_check = """
                        SELECT 1
                        FROM ohrm_attendance_record
                        WHERE employee_id = %s
                          AND punch_in_utc_time = %s
                        """
            cursor.execute(dup_check, (record.formatted_employee_id, utc_time_str))
            if cursor.fetchone():
                logging.debug(f"Duplicate punch-in found for employee {record.formatted_employee_id} at {utc_time_str}")
                return 'duplicate'

            # Insert new punch-in record
            data = (
                record.formatted_employee_id,
                user_time_str,  # punch_in_user_time
                utc_time_str,  # punch_in_utc_time
                record.offset,  # punch_in_time_offset
                self.config.default_timezone,  # punch_in_timezone_name
                None, None, None, None,  # punch-out fields
                'PUNCHED IN', 'Biometric System', ''  # state and notes
            )
            cursor.execute(self.get_insert_query(), data)

            # Verify the insert was successful
            if cursor.rowcount > 0:
                logging.debug(f"Successfully inserted punch-in for employee {record.formatted_employee_id}")
                return 'transferred'
            else:
                logging.warning(f"Insert operation did not affect any rows for employee {record.formatted_employee_id}")
                return 'error'

        except Exception as e:
            logging.error(f"Error processing punch-in for record ID={record.id}: {e}")
            return 'error'

    def _process_punch_out(self, cursor, record):
        """
        Process a punch-out record with improved matching logic

        Args:
            cursor: Database cursor
            record: AttendanceRecord instance

        Returns:
            str: 'transferred', 'duplicate', or 'error'
        """
        try:
            # Convert datetime objects to string in ISO format
            user_time_str = record.check_time.strftime('%Y-%m-%d %H:%M:%S')
            utc_time_str = record.utc_time.strftime('%Y-%m-%d %H:%M:%S')

            # Check for duplicate with more specific criteria
            dup_check = """
                        SELECT 1
                        FROM ohrm_attendance_record
                        WHERE employee_id = %s
                          AND punch_out_utc_time = %s
                        """
            cursor.execute(dup_check, (record.formatted_employee_id, utc_time_str))
            if cursor.fetchone():
                logging.debug(f"Duplicate punch-out found for employee {record.formatted_employee_id} at {utc_time_str}")
                return 'duplicate'

            # Try to find the most appropriate punch-in record to update
            # Look for punch-in records within the same day that don't have punch-out
            find_query = """
                         SELECT id, punch_in_utc_time
                         FROM ohrm_attendance_record
                         WHERE employee_id = %s
                           AND punch_out_utc_time IS NULL
                           AND punch_in_utc_time < %s
                           AND DATE(punch_in_utc_time) = DATE(%s)
                         ORDER BY punch_in_utc_time DESC
                         LIMIT 1
                         """
            cursor.execute(find_query, (record.formatted_employee_id, utc_time_str, utc_time_str))
            record_result = cursor.fetchone()

            if record_result:
                # Update existing punch-in record with punch-out time
                record_id = record_result[0]
                punch_in_time = record_result[1]

                update_query = """
                               UPDATE ohrm_attendance_record
                               SET punch_out_user_time     = %s,
                                   punch_out_utc_time      = %s,
                                   punch_out_time_offset   = %s,
                                   punch_out_timezone_name = %s,
                                   state                   = 'PUNCHED OUT',
                                   punch_out_note          = %s
                               WHERE id = %s
                               """
                update_data = (
                    user_time_str,  # punch_out_user_time
                    utc_time_str,  # punch_out_utc_time
                    record.offset,  # punch_out_time_offset
                    self.config.default_timezone,  # punch_out_timezone_name
                    'Biometric System',  # punch_out_note
                    record_id  # record ID to update
                )
                cursor.execute(update_query, update_data)

                # Verify the update was successful
                if cursor.rowcount > 0:
                    logging.debug(f"Successfully updated punch-out for employee {record.formatted_employee_id}, "
                                f"punch-in was at {punch_in_time}")
                    return 'transferred'
                else:
                    logging.warning(f"Update operation did not affect any rows for employee {record.formatted_employee_id}")
                    return 'error'
            else:
                # No matching punch-in found - create new record with only punch-out
                logging.info(f"No matching punch-in found for employee {record.formatted_employee_id}, "
                           f"creating standalone punch-out record")

                data = (
                    record.formatted_employee_id,
                    None, None, None, None,  # punch-in fields
                    user_time_str, utc_time_str, record.offset, self.config.default_timezone,
                    'PUNCHED OUT', '', 'Biometric System'
                )
                cursor.execute(self.get_insert_query(), data)

                # Verify the insert was successful
                if cursor.rowcount > 0:
                    logging.debug(f"Successfully inserted standalone punch-out for employee {record.formatted_employee_id}")
                    return 'transferred'
                else:
                    logging.warning(f"Insert operation did not affect any rows for employee {record.formatted_employee_id}")
                    return 'error'

        except Exception as e:
            logging.error(f"Error processing punch-out for record ID={record.id}: {e}")
            return 'error'

# Maintain backward compatibility
def transfer_attendance_data():
    """Main function to transfer attendance data"""
    # Use the old approach for backward compatibility with tests
    source_conn = create_source_connection()
    orange_conn = create_orangehrm_connection()

    if not source_conn or not orange_conn:
        return

    attendance_records = get_attendance_records(source_conn)
    if not attendance_records:
        logging.info("No attendance records found for today")
        return

    transferred = 0
    duplicates = 0
    errors = 0

    try:
        orange_cursor = orange_conn.cursor()

        for record_data in attendance_records:
            try:
                # Validate employee ID
                is_valid, error_msg = validate_employee_id(record_data['employeeID'])
                if not is_valid:
                    logging.warning(f"Skipping record with invalid employeeID: ID={record_data['AttID']}, Error: {error_msg}")
                    errors += 1
                    continue

                # Format employeeID as 4-digit string
                emp_id = str(record_data['employeeID']).zfill(4)

                # Validate checkTime
                if record_data['checkTime'] is None:
                    logging.warning(f"Skipping record with null checkTime: ID={record_data['AttID']}, EmpID={emp_id}")
                    errors += 1
                    continue

                # Validate employee exists in OrangeHRM
                emp_check = "SELECT employee_id FROM hs_hr_employee WHERE employee_id = %s"
                orange_cursor.execute(emp_check, (emp_id,))
                if not orange_cursor.fetchone():
                    logging.warning(f"Employee {emp_id} not found in OrangeHRM")
                    continue

                # Convert time to UTC and offset using DEFAULT_TIMEZONE
                utc_time, offset = convert_to_utc_and_offset(record_data['checkTime'])

                # Convert datetime objects to string in ISO format
                user_time_str = record_data['checkTime'].strftime('%Y-%m-%d %H:%M:%S')
                utc_time_str = utc_time.strftime('%Y-%m-%d %H:%M:%S')

                # Process based on checkType
                check_type = str(record_data['checkType']).strip().upper()
                if check_type in ['IN', 'I']:
                    # PUNCH-IN: Check for duplicate
                    dup_check = """
                                SELECT 1
                                FROM ohrm_attendance_record
                                WHERE employee_id = %s
                                  AND punch_in_utc_time = %s
                                """
                    orange_cursor.execute(dup_check, (emp_id, utc_time_str))
                    if orange_cursor.fetchone():
                        duplicates += 1
                        continue

                    # Insert new punch-in record
                    insert_query = """
                                   INSERT INTO ohrm_attendance_record (employee_id,
                                                                       punch_in_user_time, punch_in_utc_time, punch_in_time_offset,
                                                                       punch_in_timezone_name,
                                                                       punch_out_user_time, punch_out_utc_time, punch_out_time_offset,
                                                                       punch_out_timezone_name,
                                                                       state, punch_in_note, punch_out_note)
                                   VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                   """
                    data = (
                        emp_id,
                        user_time_str,  # punch_in_user_time
                        utc_time_str,  # punch_in_utc_time
                        offset,  # punch_in_time_offset
                        DEFAULT_TIMEZONE,  # punch_in_timezone_name
                        None, None, None, None,  # punch-out fields
                        'PUNCHED IN', 'Biometric System', ''  # state and notes
                    )
                    orange_cursor.execute(insert_query, data)
                    transferred += 1

                elif check_type in ['OUT', 'O']:
                    # PUNCH-OUT: Check for duplicate
                    dup_check = """
                                SELECT 1
                                FROM ohrm_attendance_record
                                WHERE employee_id = %s
                                  AND punch_out_utc_time = %s
                                """
                    orange_cursor.execute(dup_check, (emp_id, utc_time_str))
                    if orange_cursor.fetchone():
                        duplicates += 1
                        continue

                    # Try to update most recent matching punch-in record
                    # First, find the most recent punch-in record ID
                    find_query = """
                                 SELECT id FROM ohrm_attendance_record
                                 WHERE employee_id = %s
                                   AND punch_out_utc_time IS NULL
                                   AND punch_in_utc_time < %s
                                 ORDER BY punch_in_utc_time DESC
                                 LIMIT 1
                                 """
                    orange_cursor.execute(find_query, (emp_id, utc_time_str))
                    record_result = orange_cursor.fetchone()

                    if record_result:
                        record_id = record_result[0]
                        update_query = """
                                       UPDATE ohrm_attendance_record
                                       SET punch_out_user_time     = %s,
                                           punch_out_utc_time      = %s,
                                           punch_out_time_offset   = %s,
                                           punch_out_timezone_name = %s,
                                           state                   = 'PUNCHED OUT',
                                           punch_out_note          = %s
                                       WHERE id = %s
                                       """
                        update_data = (
                            user_time_str,  # punch_out_user_time
                            utc_time_str,  # punch_out_utc_time
                            offset,  # punch_out_time_offset
                            DEFAULT_TIMEZONE,  # punch_out_timezone_name
                            'Biometric System',  # punch_out_note
                            record_id  # record ID to update
                        )
                        orange_cursor.execute(update_query, update_data)
                        transferred += 1
                    else:
                        # No matching punch-in found - create new record
                        insert_query = """
                                       INSERT INTO ohrm_attendance_record (employee_id,
                                                                           punch_in_user_time, punch_in_utc_time, punch_in_time_offset,
                                                                           punch_in_timezone_name,
                                                                           punch_out_user_time, punch_out_utc_time, punch_out_time_offset,
                                                                           punch_out_timezone_name,
                                                                           state, punch_in_note, punch_out_note)
                                       VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                       """
                        data = (
                            emp_id,
                            None, None, None, None,  # punch-in fields
                            user_time_str, utc_time_str, offset, DEFAULT_TIMEZONE,
                            'PUNCHED OUT', '', 'Biometric System'
                        )
                        orange_cursor.execute(insert_query, data)
                        transferred += 1

                else:
                    logging.warning(f"Unknown checkType '{check_type}' for record ID={record_data['AttID']}")
                    errors += 1

            except Exception as e:
                errors += 1
                logging.error(f"Error processing record ID={record_data.get('AttID')}: {e}")
                logging.error(f"Record details: {record_data}")

        orange_conn.commit()
        logging.info(f"Transfer complete! Transferred: {transferred}, Duplicates: {duplicates}, Errors: {errors}")

    except Exception as e:
        logging.error(f"Database error during transfer: {e}")
        if orange_conn:
            orange_conn.rollback()
    finally:
        if source_conn:
            source_conn.close()
        if orange_conn:
            if orange_cursor:
                orange_cursor.close()
            orange_conn.close()

def smart_transfer_attendance_data():
    """
    Smart wrapper for attendance transfer that considers business hours
    """
    # Log status change if needed
    scheduler.log_status_if_changed()

    # Always run during business hours, optionally run during off-hours
    if scheduler.is_business_hours():
        logging.debug("Running transfer during business hours")
        transfer_attendance_data()
    elif config.off_hours_interval > 0:
        logging.debug("Running transfer during off-hours")
        transfer_attendance_data()
    else:
        logging.debug("Skipping transfer during off-hours (off-hours interval is 0)")

def run_smart_scheduler():
    """
    Run the smart scheduler that adapts intervals based on business hours
    """
    logging.info("Starting smart attendance transfer scheduler")
    logging.info(f"Using timezone: {DEFAULT_TIMEZONE}")
    logging.info(f"Source DB: {SOURCE_DB_CONFIG['server']}/{SOURCE_DB_CONFIG['database']}")
    logging.info(f"Target DB: {ORANGEHRM_DB_CONFIG['host']}/{ORANGEHRM_DB_CONFIG['database']}")

    # Log initial configuration
    if config.business_hours_enabled:
        business_start = f"{config.business_start_hour:02d}:{config.business_start_minute:02d}"
        business_end = f"{config.business_end_hour:02d}:{config.business_end_minute:02d}"
        business_days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        days_str = ', '.join([business_days[d-1] for d in sorted(config.business_days)])
        logging.info(f"Business hours scheduling enabled")
        logging.info(f"Business hours: {business_start}-{business_end}, {days_str}")
        logging.info(f"Business hours interval: {config.business_hours_interval} minutes")
        logging.info(f"Off-hours interval: {config.off_hours_interval} minutes")
    else:
        logging.info(f"24/7 scheduling mode - running every {config.business_hours_interval} minutes")

    # Run immediately on startup
    smart_transfer_attendance_data()

    # Clear any existing scheduled jobs
    schedule.clear()

    last_interval = None

    # Main scheduling loop
    while True:
        try:
            current_interval = scheduler.get_current_interval()

            # Reschedule if interval changed
            if current_interval != last_interval:
                schedule.clear()
                if current_interval > 0:
                    schedule.every(current_interval).minutes.do(smart_transfer_attendance_data)
                    logging.info(f"Rescheduled to run every {current_interval} minutes")
                else:
                    logging.info("Scheduling disabled (interval is 0)")
                last_interval = current_interval

            # Run pending jobs
            schedule.run_pending()
            time.sleep(60)  # Check every minute for schedule changes

        except KeyboardInterrupt:
            logging.info("Scheduler stopped by user")
            break
        except Exception as e:
            logging.error(f"Error in scheduler loop: {e}", exc_info=True)
            time.sleep(60)  # Wait before retrying

if __name__ == "__main__":
    # Check for required environment variables before starting
    if not check_required_env_vars():
        logging.error("Cannot start application due to missing environment variables")
        exit(1)

    # Run the smart scheduler
    run_smart_scheduler()