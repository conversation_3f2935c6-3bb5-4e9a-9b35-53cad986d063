# Business Hours Scheduling Tests

This document describes the comprehensive test suite for the business hours scheduling functionality added to the attendance transfer script.

## Test Files

### 1. `test_business_hours_scheduling.py`
Main test file covering core business hours scheduling functionality.

**Test Classes:**
- `TestAttendanceConfigBusinessHours`: Tests configuration loading and validation
- `TestBusinessHoursScheduler`: Tests the BusinessHoursScheduler class
- `TestSmartSchedulingFunctions`: Tests smart scheduling functions
- `TestIntegrationWithExistingCode`: Tests integration with existing codebase

**Key Test Cases:**
- Default and custom business hours configuration
- Business hours validation (invalid hours, days, intervals)
- Business hours detection during work time vs off-hours
- Boundary time testing (start/end of business hours)
- Interval calculation based on business hours
- Status message generation
- Smart transfer function behavior
- Backward compatibility

### 2. `test_business_hours_edge_cases.py`
Edge cases and error handling tests.

**Test Classes:**
- `TestBusinessHoursEdgeCases`: Comprehensive edge case testing

**Key Test Cases:**
- Timezone handling (naive vs aware datetime objects)
- Cross-timezone business hours calculation
- Daylight saving time handling
- Weekend edge cases
- Custom business days configuration (e.g., <PERSON><PERSON>-Sat)
- Midnight boundary cases
- Zero-minute intervals (disabled off-hours)
- Status message logging changes
- Error handling components

## Test Coverage

### Configuration Testing
- ✅ Default configuration values
- ✅ Custom configuration loading
- ✅ Configuration validation
- ✅ Invalid configuration error handling
- ✅ Environment variable parsing

### Business Hours Logic Testing
- ✅ Business hours detection during work time
- ✅ Business hours detection outside work time
- ✅ Boundary time testing (exact start/end times)
- ✅ Weekend detection
- ✅ Custom business days (non-standard schedules)
- ✅ Timezone handling (naive and aware datetime)
- ✅ Cross-timezone calculations
- ✅ Daylight saving time transitions

### Scheduling Logic Testing
- ✅ Interval calculation based on business hours
- ✅ Smart transfer function behavior
- ✅ Schedule clearing and rescheduling
- ✅ Zero-interval handling (disabled off-hours)
- ✅ Status message generation and logging

### Integration Testing
- ✅ Global scheduler creation
- ✅ Backward compatibility with existing functions
- ✅ Configuration object integration
- ✅ Error handling components

### Edge Cases Testing
- ✅ Timezone edge cases
- ✅ Midnight boundary cases
- ✅ Custom business day configurations
- ✅ Zero and negative interval handling
- ✅ Status message change detection

## Running the Tests

### Run All Business Hours Tests
```bash
python3 -m pytest tests/test_business_hours*.py -v
```

### Run Specific Test File
```bash
python3 -m pytest tests/test_business_hours_scheduling.py -v
python3 -m pytest tests/test_business_hours_edge_cases.py -v
```

### Run with Coverage
```bash
python3 -m pytest tests/test_business_hours*.py --cov=sqlscript --cov-report=term-missing
```

## Test Environment Setup

The tests use the following environment variables for configuration:

```bash
# Required database configuration (mocked in tests)
SOURCE_DB_SERVER=test-server
SOURCE_DB_DATABASE=test_db
SOURCE_DB_USERNAME=test_user
SOURCE_DB_PASSWORD=test_pass
ORANGEHRM_DB_HOST=test-host
ORANGEHRM_DB_USER=test_mysql_user
ORANGEHRM_DB_PASSWORD=test_mysql_pass
ORANGEHRM_DB_DATABASE=test_mysql_db

# Business hours configuration (tested with various values)
BUSINESS_HOURS_ENABLED=true
BUSINESS_START_HOUR=8
BUSINESS_START_MINUTE=0
BUSINESS_END_HOUR=17
BUSINESS_END_MINUTE=0
BUSINESS_DAYS=1,2,3,4,5
BUSINESS_HOURS_INTERVAL=5
OFF_HOURS_INTERVAL=60

# Other configuration
DEFAULT_TIMEZONE=Pacific/Guadalcanal
LOG_LEVEL=DEBUG
LOG_FILE=test_attendance.log
```

## Test Results Summary

- **Total Tests**: 27 business hours tests
- **Test Coverage**: Core functionality, edge cases, error handling
- **All Tests Passing**: ✅
- **Integration**: Maintains backward compatibility
- **Error Handling**: Comprehensive validation and error scenarios

## Key Features Tested

1. **Business Hours Detection**: Accurately determines if current time is within configured business hours
2. **Dynamic Scheduling**: Automatically adjusts transfer intervals based on business hours
3. **Timezone Support**: Handles multiple timezones and daylight saving time
4. **Configuration Validation**: Validates all business hours configuration parameters
5. **Error Handling**: Graceful handling of configuration errors and runtime exceptions
6. **Backward Compatibility**: Existing functionality continues to work unchanged
7. **Flexible Configuration**: Supports various business schedules and custom intervals

The test suite ensures the business hours scheduling feature is robust, reliable, and ready for production use.
