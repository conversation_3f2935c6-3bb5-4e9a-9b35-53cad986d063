# Frontend Setup Guide: FastAPI + shadcn/ui

This guide shows how to create a modern monitoring dashboard using FastAPI backend with shadcn/ui components.

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   FastAPI       │    │  Next.js/React   │    │  Your Existing  │
│   (API Server)  │◄──►│  + shadcn/ui     │    │  Python Script  │
│   Port 8000     │    │  (Frontend)      │    │  (Background)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Quick Start

### 1. Start the FastAPI Backend

```bash
# Install new dependencies
pip install -r requirements.txt

# Start the API server
python api_server.py
```

The API will be available at:
- **API Docs**: http://localhost:8000/api/docs
- **Health Check**: http://localhost:8000/api/health
- **System Status**: http://localhost:8000/api/status

### 2. Create the Frontend (Next.js + shadcn/ui)

```bash
# Create a new Next.js project
npx create-next-app@latest frontend --typescript --tailwind --eslint --app

cd frontend

# Initialize shadcn/ui
npx shadcn@latest init

# Install additional dependencies
npm install @tanstack/react-query axios lucide-react recharts
```

### 3. Add shadcn/ui Components

```bash
# Install the components you'll need
npx shadcn@latest add card
npx shadcn@latest add badge
npx shadcn@latest add button
npx shadcn@latest add table
npx shadcn@latest add alert
npx shadcn@latest add tabs
npx shadcn@latest add progress
npx shadcn@latest add dialog
npx shadcn@latest add toast
```

## Sample Frontend Components

### Dashboard Layout (`app/page.tsx`)

```typescript
'use client'

import { useQuery } from '@tanstack/react-query'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Activity, Database, Clock, AlertTriangle } from 'lucide-react'

const API_BASE = 'http://localhost:8000/api'

export default function Dashboard() {
  const { data: health, isLoading: healthLoading } = useQuery({
    queryKey: ['health'],
    queryFn: () => fetch(`${API_BASE}/health`).then(res => res.json()),
    refetchInterval: 30000, // Refresh every 30 seconds
  })

  const { data: status, isLoading: statusLoading } = useQuery({
    queryKey: ['status'],
    queryFn: () => fetch(`${API_BASE}/status`).then(res => res.json()),
    refetchInterval: 10000, // Refresh every 10 seconds
  })

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Attendance Transfer Monitor</h1>
        <Badge variant={health?.status === 'healthy' ? 'default' : 'destructive'}>
          {health?.status || 'Loading...'}
        </Badge>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Status</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {status?.is_running ? 'Running' : 'Stopped'}
            </div>
            <p className="text-xs text-muted-foreground">
              Uptime: {status?.uptime || 'Unknown'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Database Status</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {health?.database_connections?.source === 'healthy' && 
               health?.database_connections?.orangehrm === 'healthy' ? 'Connected' : 'Issues'}
            </div>
            <p className="text-xs text-muted-foreground">
              Source & OrangeHRM
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Business Hours</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {status?.business_hours ? 'Active' : 'Off Hours'}
            </div>
            <p className="text-xs text-muted-foreground">
              {status?.scheduler_status}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Issues</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {health?.issues?.length || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Active issues
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Issues Alert */}
      {health?.issues && health.issues.length > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>System Issues Detected:</strong>
            <ul className="mt-2 list-disc list-inside">
              {health.issues.map((issue, index) => (
                <li key={index}>{issue}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Tabs for different views */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="logs">Logs</TabsTrigger>
          <TabsTrigger value="config">Configuration</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Performance Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Performance</CardTitle>
              <CardDescription>Latest transfer statistics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <div className="text-2xl font-bold">
                    {health?.performance_stats?.total_records || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">Records Processed</p>
                </div>
                <div>
                  <div className="text-2xl font-bold">
                    {health?.performance_stats?.processing_time?.toFixed(2) || 0}s
                  </div>
                  <p className="text-xs text-muted-foreground">Processing Time</p>
                </div>
                <div>
                  <div className="text-2xl font-bold">
                    {health?.performance_stats?.batch_count || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">Batches</p>
                </div>
                <div>
                  <div className="text-2xl font-bold">
                    {health?.validation_stats?.validation_errors || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">Validation Errors</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance">
          {/* Add performance charts here using recharts */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Performance charts would go here using recharts library</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs">
          {/* Add log viewer component */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Logs</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Log viewer component would go here</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="config">
          {/* Add configuration viewer */}
          <Card>
            <CardHeader>
              <CardTitle>System Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Configuration display would go here</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Manual Transfer Button */}
      <div className="flex justify-center">
        <Button size="lg" className="w-full md:w-auto">
          Trigger Manual Transfer
        </Button>
      </div>
    </div>
  )
}
```

### API Client Setup (`lib/api.ts`)

```typescript
import axios from 'axios'

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api'

export const api = axios.create({
  baseURL: API_BASE,
  timeout: 10000,
})

export const healthApi = {
  getHealth: () => api.get('/health'),
  getStatus: () => api.get('/status'),
  getDiagnostics: () => api.get('/diagnostics'),
  getConfig: () => api.get('/config'),
  getLogs: (lines = 100, level?: string) => 
    api.get('/logs', { params: { lines, level } }),
  manualTransfer: () => api.post('/transfer/manual'),
}
```

## Key Features You Get

✅ **Real-time Monitoring**: Live system status updates
✅ **Beautiful UI**: Modern shadcn/ui components
✅ **Responsive Design**: Works on desktop and mobile
✅ **Type Safety**: Full TypeScript support
✅ **Auto-refresh**: Real-time data updates
✅ **Error Handling**: Graceful error states
✅ **Manual Controls**: Trigger transfers manually
✅ **Log Viewing**: Browse system logs
✅ **Performance Metrics**: Visual performance data

## Next Steps

1. **Start the FastAPI server**: `python api_server.py`
2. **Create the frontend**: Follow the setup steps above
3. **Customize components**: Add more shadcn/ui components as needed
4. **Add charts**: Use recharts for performance visualization
5. **Deploy**: Deploy both backend and frontend

## Benefits of This Approach

- **Separation of Concerns**: Clean API/Frontend split
- **Modern Stack**: Latest React/Next.js with shadcn/ui
- **Scalable**: Easy to add new features
- **Professional**: Production-ready monitoring interface
- **Maintainable**: Well-structured codebase
