"""
FastAPI monitoring interface for the attendance transfer system.
Provides REST API endpoints for monitoring, diagnostics, and manual operations.
"""

from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel, <PERSON>
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from contextlib import asynccontextmanager
import logging
import os
import json
from pathlib import Path

# Import your existing attendance transfer system
import sqlscript
from sqlscript import AttendanceTransferService, AttendanceConfig, BusinessHoursScheduler

# Pydantic models for API responses
class HealthStatus(BaseModel):
    status: str = Field(..., description="Overall system health status")
    timestamp: str = Field(..., description="Timestamp of health check")
    database_connections: Dict[str, str] = Field(..., description="Database connection statuses")
    performance_stats: Dict[str, Any] = Field(..., description="Performance statistics")
    validation_stats: Dict[str, Any] = Field(..., description="Validation statistics")
    issues: List[str] = Field(..., description="List of current issues")

class DiagnosticsResponse(BaseModel):
    timestamp: str = Field(..., description="Timestamp of diagnostics run")
    configuration: Dict[str, Any] = Field(..., description="System configuration")
    health_status: HealthStatus = Field(..., description="Current health status")
    recent_performance: Dict[str, Any] = Field(..., description="Recent performance metrics")
    validation_stats: Dict[str, Any] = Field(..., description="Validation statistics")
    recommendations: List[str] = Field(..., description="System recommendations")

class TransferResult(BaseModel):
    transferred_count: int = Field(..., description="Number of records transferred")
    duplicate_count: int = Field(..., description="Number of duplicate records found")
    error_count: int = Field(..., description="Number of errors encountered")
    timestamp: str = Field(..., description="Transfer completion timestamp")
    duration_seconds: float = Field(..., description="Transfer duration in seconds")

class SystemStatus(BaseModel):
    is_running: bool = Field(..., description="Whether the system is currently running")
    scheduler_status: str = Field(..., description="Current scheduler status")
    business_hours: bool = Field(..., description="Whether currently in business hours")
    next_run_time: Optional[str] = Field(None, description="Next scheduled run time")
    uptime: str = Field(..., description="System uptime")

class LogEntry(BaseModel):
    timestamp: str = Field(..., description="Log entry timestamp")
    level: str = Field(..., description="Log level")
    message: str = Field(..., description="Log message")

class ConfigurationUpdate(BaseModel):
    business_hours_enabled: Optional[bool] = None
    business_start_hour: Optional[int] = None
    business_start_minute: Optional[int] = None
    business_end_hour: Optional[int] = None
    business_end_minute: Optional[int] = None
    business_hours_interval: Optional[int] = None
    off_hours_interval: Optional[int] = None

# Global service instance
service = None
start_time = datetime.now()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Initialize the service on startup"""
    try:
        global service
        service = AttendanceTransferService()
        logging.info("FastAPI monitoring server started successfully")
    except Exception as e:
        logging.error(f"Failed to start monitoring server: {e}")
    yield

# Initialize FastAPI app
app = FastAPI(
    title="Attendance Transfer Monitor",
    description="Monitoring and management interface for the biometric attendance transfer system",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    lifespan=lifespan
)

# Add CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # React/Vite dev servers
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)



def get_service() -> AttendanceTransferService:
    """Get or create the attendance transfer service instance"""
    global service
    if service is None:
        try:
            service = AttendanceTransferService()
        except Exception as e:
            logging.error(f"Failed to initialize AttendanceTransferService: {e}")
            raise HTTPException(status_code=500, detail=f"Service initialization failed: {e}")
    return service



# API Routes
@app.get("/api/health", response_model=HealthStatus)
async def get_health_status():
    """Get comprehensive system health status"""
    try:
        service = get_service()
        health_data = service.get_health_status()
        return HealthStatus(**health_data)
    except Exception as e:
        logging.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {e}")

@app.get("/api/diagnostics", response_model=DiagnosticsResponse)
async def get_diagnostics():
    """Run comprehensive system diagnostics"""
    try:
        service = get_service()
        diagnostics_data = service.run_diagnostics()
        return DiagnosticsResponse(**diagnostics_data)
    except Exception as e:
        logging.error(f"Diagnostics failed: {e}")
        raise HTTPException(status_code=500, detail=f"Diagnostics failed: {e}")

@app.get("/api/status", response_model=SystemStatus)
async def get_system_status():
    """Get current system status and scheduler information"""
    try:
        scheduler = sqlscript.scheduler
        uptime = datetime.now() - start_time
        
        return SystemStatus(
            is_running=True,  # If API is responding, system is running
            scheduler_status=scheduler.get_status_message(),
            business_hours=scheduler.is_business_hours(),
            next_run_time=None,  # Would need to integrate with schedule library
            uptime=str(uptime).split('.')[0]  # Remove microseconds
        )
    except Exception as e:
        logging.error(f"Status check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Status check failed: {e}")

@app.post("/api/transfer/manual", response_model=TransferResult)
async def manual_transfer(background_tasks: BackgroundTasks):
    """Manually trigger an attendance data transfer"""
    try:
        service = get_service()
        start_time = datetime.now()
        
        transferred, duplicates, errors = service.transfer_attendance_data()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        return TransferResult(
            transferred_count=transferred,
            duplicate_count=duplicates,
            error_count=errors,
            timestamp=end_time.isoformat(),
            duration_seconds=duration
        )
    except Exception as e:
        logging.error(f"Manual transfer failed: {e}")
        raise HTTPException(status_code=500, detail=f"Manual transfer failed: {e}")

@app.get("/api/logs")
async def get_recent_logs(lines: int = 100, level: Optional[str] = None):
    """Get recent log entries"""
    try:
        log_file = os.getenv('LOG_FILE', 'attendance_transfer.log')
        
        if not os.path.exists(log_file):
            return {"logs": [], "message": "Log file not found"}
        
        # Read last N lines from log file
        with open(log_file, 'r') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
        
        # Parse log entries (basic parsing)
        logs = []
        for line in recent_lines:
            line = line.strip()
            if line:
                # Basic log parsing - adjust based on your log format
                parts = line.split(' - ', 2)
                if len(parts) >= 3:
                    timestamp, log_level, message = parts[0], parts[1], parts[2]
                    if level is None or log_level.upper() == level.upper():
                        logs.append({
                            "timestamp": timestamp,
                            "level": log_level,
                            "message": message
                        })
        
        return {"logs": logs, "total": len(logs)}
    except Exception as e:
        logging.error(f"Failed to read logs: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to read logs: {e}")

@app.get("/api/config")
async def get_configuration():
    """Get current system configuration (read-only)"""
    try:
        config = sqlscript.config
        scheduler = sqlscript.scheduler
        
        return {
            "database": {
                "source_server": config.source_db_config.get('server', 'Not configured'),
                "source_database": config.source_db_config.get('database', 'Not configured'),
                "orangehrm_host": config.orangehrm_db_config.get('host', 'Not configured'),
                "orangehrm_database": config.orangehrm_db_config.get('database', 'Not configured'),
            },
            "scheduling": {
                "business_hours_enabled": config.business_hours_enabled,
                "business_start_hour": config.business_start_hour,
                "business_start_minute": config.business_start_minute,
                "business_end_hour": config.business_end_hour,
                "business_end_minute": config.business_end_minute,
                "business_hours_interval": config.business_hours_interval,
                "off_hours_interval": config.off_hours_interval,
                "business_days": config.business_days,
            },
            "system": {
                "default_timezone": config.default_timezone,
                "log_level": os.getenv('LOG_LEVEL', 'INFO'),
                "log_file": os.getenv('LOG_FILE', 'attendance_transfer.log'),
            }
        }
    except Exception as e:
        logging.error(f"Failed to get configuration: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get configuration: {e}")

# Serve static files for the frontend
static_dir = Path("frontend/dist")
if static_dir.exists():
    # Only mount directories that exist
    static_static_dir = static_dir / "static"
    assets_dir = static_dir / "assets"

    if static_static_dir.exists():
        app.mount("/static", StaticFiles(directory="frontend/dist/static"), name="static")
    if assets_dir.exists():
        app.mount("/assets", StaticFiles(directory="frontend/dist/assets"), name="assets")

    @app.get("/")
    async def serve_frontend():
        return FileResponse("frontend/dist/index.html")

    @app.get("/{path:path}")
    async def serve_spa(path: str):
        """Serve SPA for all non-API routes"""
        if path.startswith("api/"):
            raise HTTPException(status_code=404, detail="API endpoint not found")

        file_path = static_dir / path
        if file_path.exists() and file_path.is_file():
            return FileResponse(file_path)

        # Fallback to index.html for SPA routing
        return FileResponse("frontend/dist/index.html")
else:
    @app.get("/")
    async def no_frontend():
        return {
            "message": "Frontend not built yet",
            "instructions": "Run 'npm run build' in the frontend directory",
            "api_docs": "/api/docs"
        }

if __name__ == "__main__":
    import uvicorn
    
    # Check if environment variables are set
    if not sqlscript.check_required_env_vars():
        print("❌ Required environment variables are missing. Please check your .env file.")
        exit(1)
    
    print("🚀 Starting Attendance Transfer Monitor API...")
    print("📊 API Documentation: http://localhost:8000/api/docs")
    print("🔍 Health Check: http://localhost:8000/api/health")
    
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
